"""
Multi-Critic SAC训练脚本

演示如何使用Multi-Critic SAC算法训练海上智能体。
"""

import torch
import numpy as np
import argparse
import os
import sys
from typing import Dict, Any
import gymnasium as gym
from tianshou.env import DummyVectorEnv
from tianshou.data import Collector, VectorReplayBuffer
from tianshou.trainer import OffpolicyTrainer
from tianshou.utils import TensorboardLogger
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 导入我们的模块
from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy


def get_args() -> argparse.Namespace:
    """获取命令行参数"""
    parser = argparse.ArgumentParser()
    
    # 环境参数
    parser.add_argument('--map-size', type=float, nargs=2, default=[100.0, 100.0],
                       help='地图大小 (width, height)')
    parser.add_argument('--num-obstacles', type=int, default=10,
                       help='障碍物数量')
    parser.add_argument('--max-episode-steps', type=int, default=1000,
                       help='最大步数')
    
    # 网络参数
    parser.add_argument('--hidden-sizes', type=int, nargs='+', default=[256, 256],
                       help='隐藏层大小')
    parser.add_argument('--num-critics', type=int, default=3,
                       help='评价器数量')
    
    # 训练参数
    parser.add_argument('--lr-actor', type=float, default=3e-4,
                       help='Actor学习率')
    parser.add_argument('--lr-critic', type=float, default=3e-4,
                       help='Critic学习率')
    parser.add_argument('--gamma', type=float, default=0.99,
                       help='折扣因子')
    parser.add_argument('--tau', type=float, default=0.005,
                       help='软更新系数')
    parser.add_argument('--alpha', type=float, default=0.2,
                       help='熵正则化系数')
    parser.add_argument('--auto-alpha', action='store_true',
                       help='是否自动调整alpha')
    
    # 融合策略参数
    parser.add_argument('--fusion-strategy', type=str, default='weighted_average',
                       choices=['weighted_average', 'minimum', 'maximum', 'median'],
                       help='Q值融合策略')
    parser.add_argument('--obstacle-weight', type=float, default=0.4,
                       help='避障评价器权重')
    parser.add_argument('--navigation-weight', type=float, default=0.4,
                       help='导航评价器权重')
    parser.add_argument('--adaptation-weight', type=float, default=0.2,
                       help='环境适应评价器权重')
    
    # 训练设置
    parser.add_argument('--epoch', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--step-per-epoch', type=int, default=10000,
                       help='每轮步数')
    parser.add_argument('--step-per-collect', type=int, default=10,
                       help='每次收集步数')
    parser.add_argument('--update-per-step', type=float, default=0.1,
                       help='每步更新次数')
    parser.add_argument('--batch-size', type=int, default=256,
                       help='批次大小')
    parser.add_argument('--buffer-size', type=int, default=1000000,
                       help='经验回放缓冲区大小')
    
    # 环境设置
    parser.add_argument('--training-num', type=int, default=10,
                       help='训练环境数量')
    parser.add_argument('--test-num', type=int, default=100,
                       help='测试环境数量')
    
    # 其他设置
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--logdir', type=str, default='./log',
                       help='日志目录')
    parser.add_argument('--resume-path', type=str, default=None,
                       help='恢复训练路径')
    parser.add_argument('--save-interval', type=int, default=10,
                       help='保存间隔')
    
    return parser.parse_args()


def make_env(args: argparse.Namespace) -> MarineEnvironment:
    """创建环境"""
    return MarineEnvironment(
        map_size=tuple(args.map_size),
        max_episode_steps=args.max_episode_steps,
        num_obstacles=args.num_obstacles
    )


def create_networks(args: argparse.Namespace, env: MarineEnvironment) -> tuple:
    """创建网络"""
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape
    
    # 创建Actor网络
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=args.hidden_sizes,
        device=args.device
    )
    
    # 创建多评价器网络
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=args.num_critics,
        hidden_sizes=args.hidden_sizes,
        device=args.device
    )
    
    return actor, critics


def create_optimizers(args: argparse.Namespace, actor: ActorNetwork,
                     critics: MultiCriticNetworks) -> tuple:
    """创建优化器（双critic架构）"""
    # Actor优化器
    actor_optim = torch.optim.Adam(actor.parameters(), lr=args.lr_actor)

    # 各评价器优化器（包含两个critic的参数）
    critic_optims = {}
    for name in critics.critic_names:
        # 合并两个critic的参数
        params_1 = list(critics.get_critic(name, 1).parameters())
        params_2 = list(critics.get_critic(name, 2).parameters())
        all_params = params_1 + params_2

        critic_optims[name] = torch.optim.Adam(all_params, lr=args.lr_critic)

    return actor_optim, critic_optims


def create_policy(args: argparse.Namespace, actor: ActorNetwork, 
                 critics: MultiCriticNetworks, actor_optim, critic_optims,
                 env: MarineEnvironment) -> MultiCriticSACPolicy:
    """创建策略"""
    # 评价器权重
    critic_weights = {
        'obstacle_avoidance': args.obstacle_weight,
        'navigation_guidance': args.navigation_weight,
        'environment_adaptation': args.adaptation_weight
    }
    
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=env.action_space,
        tau=args.tau,
        gamma=args.gamma,
        alpha=args.alpha,
        auto_alpha=args.auto_alpha,
        fusion_strategy=args.fusion_strategy,
        critic_weights=critic_weights,
        device=args.device
    )
    
    return policy


def main():
    """主训练函数"""
    args = get_args()
    
    # 设置设备
    if args.device == 'auto':
        args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建环境
    env = make_env(args)
    train_envs = DummyVectorEnv([lambda: make_env(args) for _ in range(args.training_num)])
    test_envs = DummyVectorEnv([lambda: make_env(args) for _ in range(args.test_num)])
    
    # 设置环境种子
    train_envs.seed(args.seed)
    test_envs.seed(args.seed)
    
    # 创建网络和优化器
    actor, critics = create_networks(args, env)
    actor_optim, critic_optims = create_optimizers(args, actor, critics)
    
    # 创建策略
    policy = create_policy(args, actor, critics, actor_optim, critic_optims, env)
    
    # 创建数据收集器
    train_collector = Collector(
        policy, train_envs, 
        VectorReplayBuffer(args.buffer_size, args.training_num)
    )
    test_collector = Collector(policy, test_envs)
    
    # 预收集随机数据
    train_collector.reset()
    train_collector.collect(n_step=args.batch_size * args.training_num, random=True)
    
    # 创建日志记录器
    log_path = os.path.join(args.logdir, 'multi_critic_sac')
    writer = SummaryWriter(log_path)
    logger = TensorboardLogger(writer)
    
    # 定义保存函数
    def save_best_fn(policy):
        torch.save(policy.state_dict(), os.path.join(log_path, 'policy.pth'))
    
    # 定义停止函数
    def stop_fn(mean_rewards):
        return mean_rewards >= 200  # 根据任务调整
    
    # 创建训练器
    trainer = OffpolicyTrainer(
        policy=policy,
        train_collector=train_collector,
        test_collector=test_collector,
        max_epoch=args.epoch,
        step_per_epoch=args.step_per_epoch,
        step_per_collect=args.step_per_collect,
        episode_per_test=args.test_num,
        batch_size=args.batch_size,
        update_per_step=args.update_per_step,
        save_best_fn=save_best_fn,
        stop_fn=stop_fn,
        logger=logger
    )
    
    # 开始训练
    print("开始训练Multi-Critic SAC...")
    result = trainer.run()
    
    print(f"训练完成！最佳奖励: {result.best_reward:.2f}")
    print(f"训练时间: {result.timing.total_time:.2f}秒")


if __name__ == '__main__':
    main()
