# 数据流分析

本文档详细分析Multi-Critic SAC算法中数据在各组件间的传递流程，包括观测、动作、奖励、梯度的完整流向。

## 🌊 数据流概览

```mermaid
graph TD
    subgraph "环境层"
        ENV[环境状态]
        OBS[观测数据<br/>46维]
        REWARD[奖励分解]
        ENV --> OBS
        ENV --> REWARD
    end
    
    subgraph "策略层"
        POLICY[策略]
        ACTION[动作<br/>2维]
        LOGPROB[对数概率]
        POLICY --> ACTION
        POLICY --> LOGPROB
    end
    
    subgraph "网络层"
        ACTOR[Actor网络]
        CRITICS[多评价器网络]
        Q_VALUES[Q值集合]
        FUSED_Q[融合Q值]
        
        ACTOR --> ACTION
        CRITICS --> Q_VALUES
        Q_VALUES --> FUSED_Q
    end
    
    subgraph "训练层"
        LOSS[损失函数]
        GRAD[梯度]
        UPDATE[参数更新]
        
        FUSED_Q --> LOSS
        LOSS --> GRAD
        GRAD --> UPDATE
    end
    
    OBS --> POLICY
    OBS --> ACTOR
    OBS --> CRITICS
    ACTION --> ENV
    REWARD --> POLICY
    LOGPROB --> LOSS
```

## 📊 详细数据流分析

### 1. 观测数据流

#### 观测空间结构

```mermaid
graph LR
    subgraph "原始观测 [46维]"
        RADAR[雷达数据<br/>36维]
        AGENT[智能体状态<br/>6维]
        ENV_INFO[环境信息<br/>4维]
    end
    
    subgraph "雷达数据分解"
        R1[方向1距离]
        R2[方向2距离]
        R36[方向36距离]
        RADAR --> R1
        RADAR --> R2
        RADAR --> R36
    end
    
    subgraph "智能体状态分解"
        POS[位置 x,y]
        VEL[速度 vx,vy]
        HEAD[航向角 θ]
        OMEGA[角速度 ω]
        AGENT --> POS
        AGENT --> VEL
        AGENT --> HEAD
        AGENT --> OMEGA
    end
    
    subgraph "环境信息分解"
        CURRENT[洋流 cx,cy]
        WIND[风力 wx,wy]
        ENV_INFO --> CURRENT
        ENV_INFO --> WIND
    end
```

#### 观测数据传递路径

```python
# 数据流示例
def observation_flow():
    # 1. 环境生成原始状态
    raw_state = {
        'agent_position': [x, y],
        'agent_velocity': [vx, vy],
        'agent_heading': theta,
        'agent_angular_velocity': omega,
        'radar_data': [d1, d2, ..., d36],
        'ocean_current': [cx, cy],
        'wind_force': [wx, wy]
    }
    
    # 2. 组装观测向量
    observation = np.concatenate([
        radar_data,      # [36]
        agent_state,     # [6]
        env_info         # [4]
    ])  # 总计 [46]
    
    # 3. 传递给策略和网络
    return observation
```

### 2. 动作数据流

#### 动作生成流程

```mermaid
sequenceDiagram
    participant O as 观测
    participant A as Actor网络
    participant P as 策略
    participant E as 环境
    
    O->>A: 观测 [batch, 46]
    A->>A: 特征提取
    A->>A: 计算均值和标准差
    A->>A: 重参数化采样
    A->>A: tanh激活限制范围
    A-->>P: 动作 [batch, 2], 对数概率 [batch, 1]
    P->>E: 执行动作 [2]
    E-->>P: 新观测, 奖励, 终止标志
```

#### 动作空间映射

```python
# 动作数据流示例
def action_flow(observation):
    # 1. Actor网络前向传播
    features = actor.feature_extractor(observation)
    mean = actor.mean_layer(features)
    log_std = actor.log_std_layer(features)
    
    # 2. 重参数化采样
    std = torch.exp(torch.clamp(log_std, -20, 2))
    normal = Normal(mean, std)
    action_raw = normal.rsample()
    
    # 3. tanh激活和缩放
    action = torch.tanh(action_raw) * max_action
    
    # 4. 计算对数概率
    log_prob = normal.log_prob(action_raw).sum(dim=-1, keepdim=True)
    log_prob -= torch.log(max_action * (1 - action.pow(2)) + 1e-6).sum(dim=-1, keepdim=True)
    
    return action, log_prob
```

### 3. 奖励数据流

#### 奖励分解机制

```mermaid
graph TD
    subgraph "环境状态"
        STATE[当前状态]
        PREV_STATE[前一状态]
        ACTION[执行动作]
        COLLISION[碰撞检测]
    end
    
    subgraph "奖励计算"
        RF[奖励函数]
        OA_R[避障奖励]
        NG_R[导航奖励]
        EA_R[环境适应奖励]
        SURV_R[存活奖励]
        
        STATE --> RF
        PREV_STATE --> RF
        ACTION --> RF
        COLLISION --> RF
        
        RF --> OA_R
        RF --> NG_R
        RF --> EA_R
        RF --> SURV_R
    end
    
    subgraph "奖励分发"
        OA_C[避障评价器]
        NG_C[导航评价器]
        EA_C[环境适应评价器]
        TOTAL[总奖励]
        
        OA_R --> OA_C
        NG_R --> NG_C
        EA_R --> EA_C
        
        OA_R --> TOTAL
        NG_R --> TOTAL
        EA_R --> TOTAL
        SURV_R --> TOTAL
    end
```

#### 奖励分量计算

```python
# 奖励数据流示例
def reward_flow(state_info, action, collision):
    # 1. 计算各个奖励分量
    obstacle_reward = compute_obstacle_avoidance_reward(
        state_info['radar_data'], collision
    )
    
    navigation_reward = compute_navigation_guidance_reward(
        state_info['position'], 
        state_info['target_position'],
        state_info['previous_position'],
        state_info['heading'],
        state_info['velocity']
    )
    
    adaptation_reward = compute_environment_adaptation_reward(
        state_info['velocity'],
        state_info['ocean_current'],
        state_info['wind_force'],
        action,
        state_info['energy_consumption']
    )
    
    survival_reward = 1.0  # 基础存活奖励
    
    # 2. 组装奖励字典
    reward_components = {
        'obstacle_avoidance': obstacle_reward,
        'navigation_guidance': navigation_reward,
        'environment_adaptation': adaptation_reward,
        'survival': survival_reward,
        'total': obstacle_reward + navigation_reward + adaptation_reward + survival_reward
    }
    
    return reward_components
```

### 4. Q值计算数据流

#### 双Critic架构数据流

```mermaid
graph TD
    subgraph "输入层"
        STATE[状态 [batch, 46]]
        ACTION[动作 [batch, 2]]
    end
    
    subgraph "避障评价器"
        OA_Q1[Q1_避障]
        OA_Q2[Q2_避障]
        OA_MIN[min(Q1,Q2)_避障]
        
        STATE --> OA_Q1
        ACTION --> OA_Q1
        STATE --> OA_Q2
        ACTION --> OA_Q2
        OA_Q1 --> OA_MIN
        OA_Q2 --> OA_MIN
    end
    
    subgraph "导航评价器"
        NG_Q1[Q1_导航]
        NG_Q2[Q2_导航]
        NG_MIN[min(Q1,Q2)_导航]
        
        STATE --> NG_Q1
        ACTION --> NG_Q1
        STATE --> NG_Q2
        ACTION --> NG_Q2
        NG_Q1 --> NG_MIN
        NG_Q2 --> NG_MIN
    end
    
    subgraph "环境适应评价器"
        EA_Q1[Q1_环境]
        EA_Q2[Q2_环境]
        EA_MIN[min(Q1,Q2)_环境]
        
        STATE --> EA_Q1
        ACTION --> EA_Q1
        STATE --> EA_Q2
        ACTION --> EA_Q2
        EA_Q1 --> EA_MIN
        EA_Q2 --> EA_MIN
    end
    
    subgraph "融合层"
        WEIGHTS[权重配置]
        FUSION[融合策略]
        FINAL_Q[最终Q值]
        
        OA_MIN --> FUSION
        NG_MIN --> FUSION
        EA_MIN --> FUSION
        WEIGHTS --> FUSION
        FUSION --> FINAL_Q
    end
```

#### Q值融合算法

```python
# Q值融合数据流示例
def q_value_flow(state, action, critics, weights, strategy):
    # 1. 计算各评价器的双Q值
    q_values = {}
    for name in critics.critic_names:
        q1 = critics.get_critic(name, 1)(state, action)
        q2 = critics.get_critic(name, 2)(state, action)
        q_values[name] = torch.min(q1, q2)  # 取最小值
    
    # 2. 根据策略融合Q值
    if strategy == 'weighted_average':
        fused_q = sum(weights[name] * q_values[name] for name in q_values.keys())
    elif strategy == 'minimum':
        fused_q = torch.min(torch.stack(list(q_values.values())), dim=0)[0]
    elif strategy == 'maximum':
        fused_q = torch.max(torch.stack(list(q_values.values())), dim=0)[0]
    
    return fused_q, q_values
```

### 5. 梯度数据流

#### 训练时的梯度传播

```mermaid
sequenceDiagram
    participant B as 批次数据
    participant P as 策略
    participant C as 评价器
    participant A as Actor
    participant O as 优化器
    
    B->>P: 训练批次
    P->>P: 提取奖励分量
    
    loop 更新每个评价器
        P->>C: 计算当前Q值
        P->>C: 计算目标Q值
        P->>C: 计算TD误差
        C->>O: 反向传播梯度
        O->>C: 更新参数
    end
    
    P->>A: 计算策略损失
    A->>O: 反向传播梯度
    O->>A: 更新参数
    
    P->>P: 软更新目标网络
```

#### 梯度计算细节

```python
# 梯度数据流示例
def gradient_flow(batch, policy):
    # 1. 准备数据
    obs = torch.tensor(batch.obs, device=device)
    action = torch.tensor(batch.act, device=device)
    reward_components = extract_reward_components(batch)
    next_obs = torch.tensor(batch.obs_next, device=device)
    done = torch.tensor(batch.done, device=device)
    
    # 2. 更新评价器
    for name in policy.critics.critic_names:
        # 获取奖励分量
        reward = reward_components[name]
        
        # 计算目标Q值
        with torch.no_grad():
            next_action, next_log_prob = policy.actor(next_obs)
            target_q1 = policy.critics_target.get_critic(name, 1)(next_obs, next_action)
            target_q2 = policy.critics_target.get_critic(name, 2)(next_obs, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_value = reward + policy.gamma * (1 - done) * (target_q - policy.alpha * next_log_prob)
        
        # 计算当前Q值和损失
        current_q1 = policy.critics.get_critic(name, 1)(obs, action)
        current_q2 = policy.critics.get_critic(name, 2)(obs, action)
        
        loss1 = F.mse_loss(current_q1, target_value)
        loss2 = F.mse_loss(current_q2, target_value)
        
        # 梯度反向传播
        policy.critic_optims[name].zero_grad()
        loss1.backward(retain_graph=True)
        loss2.backward()
        policy.critic_optims[name].step()
    
    # 3. 更新Actor
    action_new, log_prob = policy.actor(obs)
    q_values = policy.critics(obs, action_new)
    fused_q = policy.fusion_strategies.fuse_q_values(q_values, policy.critic_weights)
    
    actor_loss = (policy.alpha * log_prob - fused_q).mean()
    
    policy.actor_optim.zero_grad()
    actor_loss.backward()
    policy.actor_optim.step()
    
    return actor_loss, critic_losses
```

## 📈 数据流性能分析

### 内存使用模式

```mermaid
graph LR
    subgraph "前向传播"
        F1[观测缓存<br/>46 × batch_size]
        F2[特征缓存<br/>256 × batch_size]
        F3[Q值缓存<br/>6 × batch_size]
        F4[融合缓存<br/>1 × batch_size]
        
        F1 --> F2 --> F3 --> F4
    end
    
    subgraph "反向传播"
        B1[梯度缓存<br/>网络参数量]
        B2[中间梯度<br/>隐藏层大小]
        B3[损失梯度<br/>1 × batch_size]
        
        B3 --> B2 --> B1
    end
```

### 计算复杂度

| 组件 | 时间复杂度 | 空间复杂度 | 备注 |
|------|------------|------------|------|
| Actor前向 | O(batch × hidden) | O(batch × hidden) | 单次前向传播 |
| Critic前向 | O(6 × batch × hidden) | O(6 × batch × hidden) | 6个critic网络 |
| Q值融合 | O(3 × batch) | O(3 × batch) | 3个目标融合 |
| 梯度计算 | O(params) | O(params) | 参数数量线性 |

### 数据传输优化

```python
# 数据传输优化示例
def optimized_data_flow():
    # 1. 批量处理减少传输次数
    batch_obs = torch.stack([obs1, obs2, ..., obs_n])
    
    # 2. 就地操作减少内存分配
    action.clamp_(-1.0, 1.0)  # 就地限制范围
    
    # 3. 梯度累积减少更新频率
    if step % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
    
    # 4. 混合精度减少内存使用
    with torch.cuda.amp.autocast():
        q_values = critics(obs, action)
```

## 🔧 数据流调试

### 数据形状验证

```python
def validate_data_shapes():
    """验证数据流中各阶段的张量形状"""
    batch_size = 32
    
    # 观测形状检查
    assert obs.shape == (batch_size, 46), f"观测形状错误: {obs.shape}"
    
    # 动作形状检查
    assert action.shape == (batch_size, 2), f"动作形状错误: {action.shape}"
    
    # Q值形状检查
    for name, q_value in q_values.items():
        assert q_value.shape == (batch_size, 1), f"{name} Q值形状错误: {q_value.shape}"
    
    # 融合Q值形状检查
    assert fused_q.shape == (batch_size, 1), f"融合Q值形状错误: {fused_q.shape}"
```

### 数据范围检查

```python
def validate_data_ranges():
    """验证数据流中各阶段的数值范围"""
    # 动作范围检查
    assert torch.all(action >= -1.0) and torch.all(action <= 1.0), "动作超出范围"
    
    # 概率范围检查
    assert torch.all(log_prob <= 0), "对数概率应为负值"
    
    # Q值合理性检查
    for name, q_value in q_values.items():
        assert torch.all(torch.isfinite(q_value)), f"{name} Q值包含无穷大或NaN"
```

这个数据流分析文档提供了Multi-Critic SAC算法中数据传递的完整视图，有助于理解算法的内部工作机制和优化性能。
