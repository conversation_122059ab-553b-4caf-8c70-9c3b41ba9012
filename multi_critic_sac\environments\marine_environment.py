"""
海上智能体环境实现

实现了符合Gymnasium接口的海上智能体环境，包含：
- 雷达传感器模拟
- 海洋环境力（洋流、风力）
- 智能体动力学模型
- 障碍物和目标管理
"""

import gymnasium as gym
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import math
from gymnasium import spaces

from .reward_functions import RewardFunctions


class MarineEnvironment(gym.Env):
    """
    海上智能体环境
    
    模拟海上智能体的导航任务，包含避障、目标导航和环境适应等挑战。
    
    观测空间：
    - 雷达数据：36个方向的距离值 [36]
    - 智能体状态：位置、速度、航向角 [6]
    - 环境信息：洋流、风力 [4]
    总计：46维观测空间
    
    动作空间：
    - 推进力：[-1, 1]
    - 转向力：[-1, 1]
    """
    
    def __init__(
        self,
        map_size: Tuple[float, float] = (100.0, 100.0),
        max_episode_steps: int = 1000,
        num_obstacles: int = 10,
        radar_range: float = 20.0,
        radar_beams: int = 36,
        max_thrust: float = 5.0,
        max_steering: float = 2.0,
        dt: float = 0.1,
        seed: Optional[int] = None
    ):
        """
        初始化海上环境
        
        Args:
            map_size: 地图大小 (width, height)
            max_episode_steps: 最大步数
            num_obstacles: 障碍物数量
            radar_range: 雷达探测范围
            radar_beams: 雷达波束数量
            max_thrust: 最大推进力
            max_steering: 最大转向力
            dt: 时间步长
            seed: 随机种子
        """
        super().__init__()
        
        # 环境参数
        self.map_size = map_size
        self.max_episode_steps = max_episode_steps
        self.num_obstacles = num_obstacles
        self.radar_range = radar_range
        self.radar_beams = radar_beams
        self.max_thrust = max_thrust
        self.max_steering = max_steering
        self.dt = dt
        
        # 定义观测空间和动作空间
        # 观测空间：雷达(36) + 状态(6) + 环境(4) = 46
        obs_low = np.concatenate([
            np.zeros(radar_beams),  # 雷达距离 [0, radar_range]
            [-map_size[0]/2, -map_size[1]/2],  # 位置
            [-20.0, -20.0],  # 速度
            [-np.pi],  # 航向角
            [-5.0],  # 角速度
            [-5.0, -5.0],  # 洋流
            [-10.0, -10.0]  # 风力
        ])
        
        obs_high = np.concatenate([
            np.full(radar_beams, radar_range),  # 雷达距离
            [map_size[0]/2, map_size[1]/2],  # 位置
            [20.0, 20.0],  # 速度
            [np.pi],  # 航向角
            [5.0],  # 角速度
            [5.0, 5.0],  # 洋流
            [10.0, 10.0]  # 风力
        ])
        
        self.observation_space = spaces.Box(
            low=obs_low, high=obs_high, dtype=np.float32
        )
        
        # 动作空间：推进力和转向力
        self.action_space = spaces.Box(
            low=np.array([-1.0, -1.0]),
            high=np.array([1.0, 1.0]),
            dtype=np.float32
        )
        
        # 初始化状态变量
        self.agent_position = np.zeros(2)
        self.agent_velocity = np.zeros(2)
        self.agent_heading = 0.0
        self.agent_angular_velocity = 0.0
        self.target_position = np.zeros(2)
        self.obstacles = []
        self.ocean_current = np.zeros(2)
        self.wind_force = np.zeros(2)
        
        # 历史信息
        self.previous_position = np.zeros(2)
        self.step_count = 0
        
        # 奖励函数
        self.reward_functions = RewardFunctions()
        
        # 随机数生成器
        self.np_random = None
        if seed is not None:
            self.seed(seed)
            
    def seed(self, seed: Optional[int] = None) -> List[int]:
        """设置随机种子"""
        self.np_random, seed = gym.utils.seeding.np_random(seed)
        return [seed]
        
    def reset(
        self, 
        seed: Optional[int] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        重置环境
        
        Args:
            seed: 随机种子
            options: 重置选项
            
        Returns:
            observation: 初始观测
            info: 信息字典
        """
        if seed is not None:
            self.seed(seed)
            
        # 重置智能体状态
        self._reset_agent()
        
        # 重置目标位置
        self._reset_target()
        
        # 重置障碍物
        self._reset_obstacles()
        
        # 重置环境力
        self._reset_environment_forces()
        
        # 重置计数器
        self.step_count = 0
        self.previous_position = self.agent_position.copy()
        
        # 获取初始观测
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
        
    def step(
        self, 
        action: np.ndarray
    ) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """
        执行一步动作
        
        Args:
            action: 动作 [thrust, steering]
            
        Returns:
            observation: 新观测
            reward: 奖励
            terminated: 是否终止
            truncated: 是否截断
            info: 信息字典
        """
        # 保存上一步位置
        self.previous_position = self.agent_position.copy()
        
        # 执行动作
        self._apply_action(action)
        
        # 更新智能体状态
        self._update_agent_dynamics()
        
        # 更新环境力（可选：动态变化）
        self._update_environment_forces()
        
        # 检查碰撞
        collision_occurred = self._check_collision()
        
        # 计算奖励
        state_info = self._get_state_info()
        reward_dict = self.reward_functions.compute_total_reward(
            state_info, action, collision_occurred
        )
        reward = reward_dict['total']

        # 检查终止条件
        terminated = collision_occurred or self._check_target_reached()

        # 检查截断条件
        self.step_count += 1
        truncated = self.step_count >= self.max_episode_steps

        # 获取观测和信息
        observation = self._get_observation()
        info = self._get_info()
        info.update(reward_dict)
        info['collision'] = collision_occurred

        # 添加分解的奖励信息供Multi-Critic使用
        info['reward_components'] = {
            'obstacle_avoidance': reward_dict['obstacle_avoidance'],
            'navigation_guidance': reward_dict['navigation_guidance'],
            'environment_adaptation': reward_dict['environment_adaptation']
        }

        return observation, reward, terminated, truncated, info

    def _reset_agent(self) -> None:
        """重置智能体状态"""
        # 随机初始位置（避开边界）
        margin = 10.0
        self.agent_position = self.np_random.uniform(
            [-self.map_size[0]/2 + margin, -self.map_size[1]/2 + margin],
            [self.map_size[0]/2 - margin, self.map_size[1]/2 - margin]
        )

        # 初始速度和航向
        self.agent_velocity = np.zeros(2)
        self.agent_heading = self.np_random.uniform(-np.pi, np.pi)
        self.agent_angular_velocity = 0.0

    def _reset_target(self) -> None:
        """重置目标位置"""
        # 确保目标与智能体有一定距离
        min_distance = 20.0
        max_attempts = 100

        for _ in range(max_attempts):
            self.target_position = self.np_random.uniform(
                [-self.map_size[0]/2 + 5, -self.map_size[1]/2 + 5],
                [self.map_size[0]/2 - 5, self.map_size[1]/2 - 5]
            )

            distance = np.linalg.norm(self.target_position - self.agent_position)
            if distance >= min_distance:
                break

    def _reset_obstacles(self) -> None:
        """重置障碍物"""
        self.obstacles = []
        min_distance_to_agent = 15.0
        min_distance_to_target = 10.0
        min_distance_between_obstacles = 8.0

        for _ in range(self.num_obstacles):
            max_attempts = 100
            for _ in range(max_attempts):
                # 随机障碍物位置和大小
                position = self.np_random.uniform(
                    [-self.map_size[0]/2 + 5, -self.map_size[1]/2 + 5],
                    [self.map_size[0]/2 - 5, self.map_size[1]/2 - 5]
                )
                radius = self.np_random.uniform(2.0, 5.0)

                # 检查与智能体和目标的距离
                dist_to_agent = np.linalg.norm(position - self.agent_position)
                dist_to_target = np.linalg.norm(position - self.target_position)

                if (dist_to_agent >= min_distance_to_agent and
                    dist_to_target >= min_distance_to_target):

                    # 检查与其他障碍物的距离
                    valid = True
                    for obs in self.obstacles:
                        dist = np.linalg.norm(position - obs['position'])
                        if dist < min_distance_between_obstacles:
                            valid = False
                            break

                    if valid:
                        self.obstacles.append({
                            'position': position,
                            'radius': radius
                        })
                        break

    def _reset_environment_forces(self) -> None:
        """重置环境力"""
        # 洋流：随机方向和强度
        current_angle = self.np_random.uniform(0, 2 * np.pi)
        current_magnitude = self.np_random.uniform(0.5, 3.0)
        self.ocean_current = current_magnitude * np.array([
            np.cos(current_angle), np.sin(current_angle)
        ])

        # 风力：随机方向和强度
        wind_angle = self.np_random.uniform(0, 2 * np.pi)
        wind_magnitude = self.np_random.uniform(0.0, 5.0)
        self.wind_force = wind_magnitude * np.array([
            np.cos(wind_angle), np.sin(wind_angle)
        ])

    def _apply_action(self, action: np.ndarray) -> None:
        """应用动作到智能体"""
        thrust = action[0] * self.max_thrust
        steering = action[1] * self.max_steering

        # 推进力沿当前航向方向
        thrust_force = thrust * np.array([
            np.cos(self.agent_heading),
            np.sin(self.agent_heading)
        ])

        # 更新角速度
        self.agent_angular_velocity += steering * self.dt

        # 限制角速度
        max_angular_velocity = 3.0
        self.agent_angular_velocity = np.clip(
            self.agent_angular_velocity,
            -max_angular_velocity,
            max_angular_velocity
        )

        # 应用推进力到速度
        mass = 1.0  # 智能体质量
        self.agent_velocity += (thrust_force / mass) * self.dt

    def _update_agent_dynamics(self) -> None:
        """更新智能体动力学"""
        # 应用环境力
        self.agent_velocity += (self.ocean_current + self.wind_force * 0.1) * self.dt

        # 应用阻力
        drag_coefficient = 0.1
        drag_force = -drag_coefficient * self.agent_velocity * np.linalg.norm(self.agent_velocity)
        self.agent_velocity += drag_force * self.dt

        # 更新位置
        self.agent_position += self.agent_velocity * self.dt

        # 更新航向
        self.agent_heading += self.agent_angular_velocity * self.dt

        # 限制航向角在[-π, π]范围内
        self.agent_heading = ((self.agent_heading + np.pi) % (2 * np.pi)) - np.pi

        # 边界处理：反弹
        if self.agent_position[0] < -self.map_size[0]/2:
            self.agent_position[0] = -self.map_size[0]/2
            self.agent_velocity[0] = abs(self.agent_velocity[0])
        elif self.agent_position[0] > self.map_size[0]/2:
            self.agent_position[0] = self.map_size[0]/2
            self.agent_velocity[0] = -abs(self.agent_velocity[0])

        if self.agent_position[1] < -self.map_size[1]/2:
            self.agent_position[1] = -self.map_size[1]/2
            self.agent_velocity[1] = abs(self.agent_velocity[1])
        elif self.agent_position[1] > self.map_size[1]/2:
            self.agent_position[1] = self.map_size[1]/2
            self.agent_velocity[1] = -abs(self.agent_velocity[1])

    def _update_environment_forces(self) -> None:
        """更新环境力（可选：添加动态变化）"""
        # 可以在这里添加时变的环境力
        pass

    def _get_radar_data(self) -> np.ndarray:
        """获取雷达传感器数据"""
        radar_data = np.full(self.radar_beams, self.radar_range)

        # 计算每个雷达波束的角度
        angles = np.linspace(0, 2 * np.pi, self.radar_beams, endpoint=False)

        for i, angle in enumerate(angles):
            # 雷达波束的绝对角度
            beam_angle = self.agent_heading + angle
            beam_direction = np.array([np.cos(beam_angle), np.sin(beam_angle)])

            min_distance = self.radar_range

            # 检测障碍物
            for obstacle in self.obstacles:
                distance = self._ray_circle_intersection(
                    self.agent_position,
                    beam_direction,
                    obstacle['position'],
                    obstacle['radius']
                )
                if distance is not None and distance < min_distance:
                    min_distance = distance

            # 检测边界
            boundary_distance = self._ray_boundary_intersection(
                self.agent_position, beam_direction
            )
            if boundary_distance < min_distance:
                min_distance = boundary_distance

            radar_data[i] = min_distance

        return radar_data

    def _ray_circle_intersection(
        self,
        ray_origin: np.ndarray,
        ray_direction: np.ndarray,
        circle_center: np.ndarray,
        circle_radius: float
    ) -> Optional[float]:
        """计算射线与圆的交点距离"""
        # 射线到圆心的向量
        to_center = circle_center - ray_origin

        # 投影长度
        proj_length = np.dot(to_center, ray_direction)

        # 如果投影为负，圆在射线后方
        if proj_length < 0:
            return None

        # 最近点到圆心的距离
        closest_point = ray_origin + proj_length * ray_direction
        distance_to_center = np.linalg.norm(closest_point - circle_center)

        # 如果距离大于半径，无交点
        if distance_to_center > circle_radius:
            return None

        # 计算交点距离
        chord_half_length = np.sqrt(circle_radius**2 - distance_to_center**2)
        intersection_distance = proj_length - chord_half_length

        return max(0, intersection_distance)

    def _ray_boundary_intersection(
        self,
        ray_origin: np.ndarray,
        ray_direction: np.ndarray
    ) -> float:
        """计算射线与边界的交点距离"""
        distances = []

        # 检查与四个边界的交点
        # 左边界
        if ray_direction[0] < 0:
            t = (-self.map_size[0]/2 - ray_origin[0]) / ray_direction[0]
            if t > 0:
                y = ray_origin[1] + t * ray_direction[1]
                if -self.map_size[1]/2 <= y <= self.map_size[1]/2:
                    distances.append(t)

        # 右边界
        if ray_direction[0] > 0:
            t = (self.map_size[0]/2 - ray_origin[0]) / ray_direction[0]
            if t > 0:
                y = ray_origin[1] + t * ray_direction[1]
                if -self.map_size[1]/2 <= y <= self.map_size[1]/2:
                    distances.append(t)

        # 下边界
        if ray_direction[1] < 0:
            t = (-self.map_size[1]/2 - ray_origin[1]) / ray_direction[1]
            if t > 0:
                x = ray_origin[0] + t * ray_direction[0]
                if -self.map_size[0]/2 <= x <= self.map_size[0]/2:
                    distances.append(t)

        # 上边界
        if ray_direction[1] > 0:
            t = (self.map_size[1]/2 - ray_origin[1]) / ray_direction[1]
            if t > 0:
                x = ray_origin[0] + t * ray_direction[0]
                if -self.map_size[0]/2 <= x <= self.map_size[0]/2:
                    distances.append(t)

        return min(distances) if distances else self.radar_range

    def _check_collision(self) -> bool:
        """检查是否发生碰撞"""
        # 检查与障碍物的碰撞
        agent_radius = 1.0  # 智能体半径

        for obstacle in self.obstacles:
            distance = np.linalg.norm(self.agent_position - obstacle['position'])
            if distance < (agent_radius + obstacle['radius']):
                return True

        return False

    def _check_target_reached(self) -> bool:
        """检查是否到达目标"""
        distance = np.linalg.norm(self.agent_position - self.target_position)
        return distance < self.reward_functions.target_tolerance

    def _get_observation(self) -> np.ndarray:
        """获取当前观测"""
        # 雷达数据
        radar_data = self._get_radar_data()

        # 智能体状态
        agent_state = np.array([
            self.agent_position[0],
            self.agent_position[1],
            self.agent_velocity[0],
            self.agent_velocity[1],
            self.agent_heading,
            self.agent_angular_velocity
        ])

        # 环境信息
        env_info = np.array([
            self.ocean_current[0],
            self.ocean_current[1],
            self.wind_force[0],
            self.wind_force[1]
        ])

        # 组合观测
        observation = np.concatenate([radar_data, agent_state, env_info])
        return observation.astype(np.float32)

    def _get_state_info(self) -> Dict[str, Any]:
        """获取状态信息用于奖励计算"""
        return {
            'radar_data': self._get_radar_data(),
            'position': self.agent_position.copy(),
            'target_position': self.target_position.copy(),
            'previous_position': self.previous_position.copy(),
            'heading': self.agent_heading,
            'velocity': self.agent_velocity.copy(),
            'ocean_current': self.ocean_current.copy(),
            'wind_force': self.wind_force.copy(),
            'energy_consumption': np.linalg.norm(self.agent_velocity) * 0.1
        }

    def _get_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            'agent_position': self.agent_position.copy(),
            'target_position': self.target_position.copy(),
            'distance_to_target': np.linalg.norm(self.agent_position - self.target_position),
            'agent_velocity': self.agent_velocity.copy(),
            'agent_heading': self.agent_heading,
            'ocean_current': self.ocean_current.copy(),
            'wind_force': self.wind_force.copy(),
            'step_count': self.step_count,
            'obstacles': [obs.copy() for obs in self.obstacles]
        }

    def render(self, mode: str = 'human') -> Optional[np.ndarray]:
        """渲染环境（简单的文本输出）"""
        if mode == 'human':
            print(f"Step: {self.step_count}")
            print(f"Agent Position: ({self.agent_position[0]:.2f}, {self.agent_position[1]:.2f})")
            print(f"Target Position: ({self.target_position[0]:.2f}, {self.target_position[1]:.2f})")
            print(f"Distance to Target: {np.linalg.norm(self.agent_position - self.target_position):.2f}")
            print(f"Agent Velocity: ({self.agent_velocity[0]:.2f}, {self.agent_velocity[1]:.2f})")
            print(f"Agent Heading: {self.agent_heading:.2f}")
            print("-" * 50)

        return None

    def close(self) -> None:
        """关闭环境"""
        pass
