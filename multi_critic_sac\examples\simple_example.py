"""
Multi-Critic SAC简单使用示例

演示如何快速开始使用Multi-Critic SAC算法。
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy


def simple_training_example():
    """简单的训练示例"""
    print("Multi-Critic SAC 简单训练示例")
    print("=" * 50)
    
    # 1. 创建环境
    print("1. 创建海上智能体环境...")
    env = MarineEnvironment(
        map_size=(50.0, 50.0),  # 较小的地图用于快速演示
        num_obstacles=5,
        max_episode_steps=200
    )
    
    # 2. 创建网络
    print("2. 创建神经网络...")
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"   使用设备: {device}")
    
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape
    
    # Actor网络
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=[128, 128],
        device=device
    )
    
    # 多评价器网络
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=3,
        hidden_sizes=[128, 128],
        device=device
    )
    
    print(f"   观测空间维度: {state_shape}")
    print(f"   动作空间维度: {action_shape}")
    print(f"   评价器数量: {len(critics.critic_names)}")
    print(f"   评价器名称: {critics.critic_names}")
    
    # 3. 创建优化器
    print("3. 创建优化器...")
    actor_optim = torch.optim.Adam(actor.parameters(), lr=3e-4)
    critic_optims = {
        name: torch.optim.Adam(critics.get_critic(name).parameters(), lr=3e-4)
        for name in critics.critic_names
    }
    
    # 4. 创建策略
    print("4. 创建Multi-Critic SAC策略...")
    critic_weights = {
        'obstacle_avoidance': 0.4,      # 避障权重
        'navigation_guidance': 0.4,     # 导航权重
        'environment_adaptation': 0.2   # 环境适应权重
    }
    
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=env.action_space,
        tau=0.005,
        gamma=0.99,
        alpha=0.2,
        auto_alpha=True,
        fusion_strategy='weighted_average',
        critic_weights=critic_weights,
        device=device
    )
    
    print(f"   融合策略: {policy.fusion_strategy}")
    print(f"   评价器权重: {policy.critic_weights}")
    
    # 5. 简单的训练循环
    print("5. 开始简单训练循环...")
    num_episodes = 5  # 少量回合用于演示
    
    for episode in range(num_episodes):
        obs, info = env.reset()
        episode_reward = 0.0
        episode_length = 0
        
        terminated = False
        truncated = False
        
        print(f"\n回合 {episode + 1}:")
        print(f"  初始位置: ({info['agent_position'][0]:.1f}, {info['agent_position'][1]:.1f})")
        print(f"  目标位置: ({info['target_position'][0]:.1f}, {info['target_position'][1]:.1f})")
        print(f"  初始距离: {info['distance_to_target']:.1f}")
        
        while not (terminated or truncated) and episode_length < 50:  # 限制步数用于演示
            # 获取动作
            with torch.no_grad():
                obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
                action = policy.actor.get_action(obs_tensor, deterministic=False)
                action = action.cpu().numpy().squeeze()
            
            # 执行动作
            next_obs, reward, terminated, truncated, info = env.step(action)
            
            episode_reward += reward
            episode_length += 1
            
            # 简单的经验回放（实际训练中应使用专门的缓冲区）
            if episode_length > 10:  # 收集一些经验后开始训练
                # 这里只是演示，实际应该使用批量数据
                pass
            
            obs = next_obs
        
        print(f"  最终位置: ({info['agent_position'][0]:.1f}, {info['agent_position'][1]:.1f})")
        print(f"  最终距离: {info['distance_to_target']:.1f}")
        print(f"  回合奖励: {episode_reward:.2f}")
        print(f"  回合长度: {episode_length}")
        print(f"  是否成功: {'是' if info['distance_to_target'] < 2.0 else '否'}")
        print(f"  是否碰撞: {'是' if info.get('collision', False) else '否'}")
    
    print("\n简单训练示例完成！")
    return policy, env


def test_fusion_strategies():
    """测试不同的融合策略"""
    print("\n测试Q值融合策略")
    print("=" * 30)
    
    from multi_critic_sac.utils.fusion_strategies import FusionStrategies
    
    # 创建融合策略实例
    fusion = FusionStrategies()
    
    # 模拟Q值
    q_values = {
        'obstacle_avoidance': torch.tensor([[10.0], [5.0], [15.0]]),
        'navigation_guidance': torch.tensor([[8.0], [12.0], [6.0]]),
        'environment_adaptation': torch.tensor([[6.0], [8.0], [10.0]])
    }
    
    weights = {
        'obstacle_avoidance': 0.4,
        'navigation_guidance': 0.4,
        'environment_adaptation': 0.2
    }
    
    print("原始Q值:")
    for name, q in q_values.items():
        print(f"  {name}: {q.squeeze().tolist()}")
    
    print(f"\n权重: {weights}")
    
    # 测试不同融合策略
    strategies = ['weighted_average', 'minimum', 'maximum', 'median']
    
    for strategy in strategies:
        fused_q = fusion.fuse_q_values(q_values, weights, strategy)
        print(f"\n{strategy} 融合结果: {fused_q.squeeze().tolist()}")


def test_reward_functions():
    """测试奖励函数"""
    print("\n测试奖励函数")
    print("=" * 20)
    
    from multi_critic_sac.environments.reward_functions import RewardFunctions
    
    reward_func = RewardFunctions()
    
    # 模拟状态信息
    state_info = {
        'radar_data': np.array([10.0, 15.0, 8.0, 20.0] + [25.0] * 32),  # 36个雷达值
        'position': np.array([10.0, 5.0]),
        'target_position': np.array([20.0, 15.0]),
        'previous_position': np.array([9.0, 4.0]),
        'heading': 0.5,
        'velocity': np.array([2.0, 1.0]),
        'ocean_current': np.array([1.0, 0.5]),
        'wind_force': np.array([0.5, -0.3]),
        'energy_consumption': 1.5
    }
    
    action = np.array([0.5, 0.2])
    
    # 计算奖励
    rewards = reward_func.compute_total_reward(state_info, action)
    
    print("奖励分量:")
    for name, value in rewards.items():
        print(f"  {name}: {value:.2f}")


def main():
    """主函数"""
    print("Multi-Critic SAC 算法演示")
    print("=" * 60)
    
    # 运行简单训练示例
    policy, env = simple_training_example()
    
    # 测试融合策略
    test_fusion_strategies()
    
    # 测试奖励函数
    test_reward_functions()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("\n要进行完整训练，请运行:")
    print("python multi_critic_sac/examples/training_script.py")
    print("\n要评估训练好的模型，请运行:")
    print("python multi_critic_sac/examples/evaluation_script.py --model-path <模型路径>")


if __name__ == '__main__':
    main()
