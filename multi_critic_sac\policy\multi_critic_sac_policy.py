"""
Multi-Critic SAC策略实现

基于Tianshou框架实现的多评价器SAC算法，支持多目标优化。
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Union
from copy import deepcopy
from dataclasses import dataclass

from tianshou.policy import BasePolicy
from tianshou.data import Batch, ReplayBuffer
from tianshou.data.types import (
    BatchWithReturnsProtocol,
    DistBatchProtocol, 
    ObsBatchProtocol,
    RolloutBatchProtocol
)

from ..networks.multi_critic_networks import MultiCriticNetworks
from ..networks.actor_network import ActorNetwork
from ..utils.fusion_strategies import FusionStrategies


@dataclass
class MultiCriticSACTrainingStats:
    """训练统计信息类"""

    actor_loss: float
    critic_losses: Dict[str, float]
    alpha_loss: float
    alpha: float
    q_values: Dict[str, float]
    fused_q_value: float

    def get_loss_stats_dict(self) -> Dict[str, float]:
        """获取损失统计字典，用于与Tianshou训练器兼容"""
        stats_dict = {
            'actor_loss': self.actor_loss,
            'alpha_loss': self.alpha_loss,
            'alpha': self.alpha,
            'fused_q_value': self.fused_q_value
        }

        # 添加各个评价器的损失
        for name, loss in self.critic_losses.items():
            stats_dict[f'critic_loss_{name}'] = loss

        # 添加各个评价器的Q值
        for name, q_value in self.q_values.items():
            stats_dict[f'q_value_{name}'] = q_value

        return stats_dict


class MultiCriticSACPolicy(BasePolicy):
    """
    Multi-Critic SAC策略实现
    
    基于SAC算法扩展的多评价器版本，支持多目标优化。
    每个评价器专门评估一个特定目标，通过融合机制组合多个Q值。
    
    Args:
        actor: Actor网络
        critics: 多评价器网络
        actor_optim: Actor优化器
        critic_optims: 评价器优化器字典
        action_space: 动作空间
        tau: 软更新系数
        gamma: 折扣因子
        alpha: 熵正则化系数
        auto_alpha: 是否自动调整alpha
        target_entropy: 目标熵值
        fusion_strategy: Q值融合策略
        critic_weights: 评价器权重
        device: 计算设备
    """
    
    def __init__(
        self,
        actor: ActorNetwork,
        critics: MultiCriticNetworks,
        actor_optim: torch.optim.Optimizer,
        critic_optims: Dict[str, torch.optim.Optimizer],
        action_space,
        tau: float = 0.005,
        gamma: float = 0.99,
        alpha: float = 0.2,
        auto_alpha: bool = True,
        target_entropy: Optional[float] = None,
        fusion_strategy: str = "weighted_average",
        critic_weights: Optional[Dict[str, float]] = None,
        device: Union[str, torch.device] = "cpu",
        **kwargs
    ) -> None:
        super().__init__(action_space=action_space, **kwargs)
        
        self.actor = actor
        self.critics = critics
        self.actor_optim = actor_optim
        self.critic_optims = critic_optims
        
        # 创建目标网络
        self.critics_target = deepcopy(critics)
        
        # 超参数
        self.tau = tau
        self.gamma = gamma
        self.device = device
        
        # 熵正则化
        self.auto_alpha = auto_alpha
        if auto_alpha:
            if target_entropy is None:
                # 默认目标熵为动作维度的负值
                self.target_entropy = -np.prod(action_space.shape)
            else:
                self.target_entropy = target_entropy
                
            # 可学习的alpha参数
            self.log_alpha = torch.zeros(1, requires_grad=True, device=device)
            self.alpha_optim = torch.optim.Adam([self.log_alpha], lr=3e-4)
        else:
            self.alpha = alpha
            
        # Q值融合策略
        self.fusion_strategy = fusion_strategy
        self.fusion_strategies = FusionStrategies()
        
        # 评价器权重
        if critic_weights is None:
            # 默认等权重
            num_critics = len(critics.critic_names)
            self.critic_weights = {name: 1.0 / num_critics for name in critics.critic_names}
        else:
            self.critic_weights = critic_weights
            
        # 确保权重归一化
        total_weight = sum(self.critic_weights.values())
        self.critic_weights = {k: v / total_weight for k, v in self.critic_weights.items()}
        
    @property
    def alpha_value(self) -> float:
        """获取当前alpha值"""
        if self.auto_alpha:
            return self.log_alpha.exp().item()
        else:
            return self.alpha
            
    def forward(
        self,
        batch: ObsBatchProtocol,
        state: Optional[Union[Dict, Batch, np.ndarray]] = None,
        **kwargs: Any
    ) -> DistBatchProtocol:
        """
        前向传播计算动作
        
        Args:
            batch: 观测批次
            state: 隐藏状态（未使用）
            
        Returns:
            包含动作和相关信息的批次
        """
        obs = batch.obs
        if not isinstance(obs, torch.Tensor):
            obs = torch.tensor(obs, dtype=torch.float32, device=self.device)
            
        # 生成动作
        action, log_prob = self.actor(obs, deterministic=False, with_logprob=True)
        
        return Batch(
            act=action.detach().cpu().numpy(),
            logp=log_prob.detach().cpu().numpy() if log_prob is not None else None,
            state=state
        )
        
    def learn(
        self,
        batch: RolloutBatchProtocol,
        batch_size: Optional[int] = None,
        repeat: int = 1,
        **kwargs: Any
    ) -> MultiCriticSACTrainingStats:
        """
        策略学习更新

        Args:
            batch: 训练批次数据
            batch_size: 批次大小
            repeat: 重复次数

        Returns:
            训练统计信息
        """
        # 准备数据
        obs = torch.tensor(batch.obs, dtype=torch.float32, device=self.device)
        action = torch.tensor(batch.act, dtype=torch.float32, device=self.device)
        reward = torch.tensor(batch.rew, dtype=torch.float32, device=self.device).unsqueeze(-1)
        next_obs = torch.tensor(batch.obs_next, dtype=torch.float32, device=self.device)
        done = torch.tensor(batch.done, dtype=torch.float32, device=self.device).unsqueeze(-1)

        # 提取分解的奖励（如果可用）
        reward_components = {}
        if hasattr(batch, 'info') and batch.info is not None:
            # 尝试从info中提取奖励分量
            for i, info in enumerate(batch.info):
                if isinstance(info, dict) and 'reward_components' in info:
                    for name in self.critics.critic_names:
                        if name not in reward_components:
                            reward_components[name] = []
                        reward_components[name].append(info['reward_components'].get(name, 0.0))

        # 如果没有分解奖励，使用总奖励的加权分配
        if not reward_components:
            for name in self.critics.critic_names:
                weight = self.critic_weights.get(name, 1.0 / len(self.critics.critic_names))
                reward_components[name] = (reward * weight).squeeze(-1).tolist()

        # 转换为tensor
        for name in reward_components:
            reward_components[name] = torch.tensor(
                reward_components[name], dtype=torch.float32, device=self.device
            ).unsqueeze(-1)
        
        # 统计信息
        actor_losses = []
        critic_losses = {name: [] for name in self.critics.critic_names}
        alpha_losses = []
        
        for _ in range(repeat):
            # 更新评价器（使用分解的奖励）
            critic_loss_dict = self._update_critics(obs, action, reward_components, next_obs, done)
            for name, loss in critic_loss_dict.items():
                critic_losses[name].append(loss)
                
            # 更新Actor
            actor_loss = self._update_actor(obs)
            actor_losses.append(actor_loss)
            
            # 更新alpha
            if self.auto_alpha:
                alpha_loss = self._update_alpha(obs)
                alpha_losses.append(alpha_loss)
                
            # 软更新目标网络
            self._soft_update_target()
            
        # 计算平均损失
        avg_actor_loss = np.mean(actor_losses)
        avg_critic_losses = {name: np.mean(losses) for name, losses in critic_losses.items()}
        avg_alpha_loss = np.mean(alpha_losses) if alpha_losses else 0.0
        
        # 计算当前Q值用于监控
        with torch.no_grad():
            current_q_values = self.critics(obs[:32], action[:32])  # 使用部分数据避免内存问题
            avg_q_values = {name: q.mean().item() for name, q in current_q_values.items()}
            
            # 计算融合后的Q值
            fused_q = self.fusion_strategies.fuse_q_values(
                current_q_values, self.critic_weights, self.fusion_strategy
            )
            avg_fused_q = fused_q.mean().item()
        
        return MultiCriticSACTrainingStats(
            actor_loss=avg_actor_loss,
            critic_losses=avg_critic_losses,
            alpha_loss=avg_alpha_loss,
            alpha=self.alpha_value,
            q_values=avg_q_values,
            fused_q_value=avg_fused_q
        )

    def _update_critics(
        self,
        obs: torch.Tensor,
        action: torch.Tensor,
        reward_components: Dict[str, torch.Tensor],
        next_obs: torch.Tensor,
        done: torch.Tensor
    ) -> Dict[str, float]:
        """
        更新所有评价器网络（双critic架构）

        Args:
            obs: 当前观测
            action: 动作
            reward_components: 分解的奖励字典
            next_obs: 下一个观测
            done: 终止标志

        Returns:
            各评价器的损失字典
        """
        # 计算下一步动作和对数概率
        with torch.no_grad():
            next_action, next_log_prob = self.actor(next_obs, deterministic=False, with_logprob=True)

        # 更新每个评价器的双critic网络
        critic_losses = {}

        for name in self.critics.critic_names:
            # 获取该评价器的奖励分量
            reward_component = reward_components.get(name, reward_components.get('total', torch.zeros_like(done)))

            # 计算目标Q值（使用目标网络的最小值）
            with torch.no_grad():
                target_q1 = self.critics_target.get_critic(name, 1)(next_obs, next_action)
                target_q2 = self.critics_target.get_critic(name, 2)(next_obs, next_action)
                target_q = torch.min(target_q1, target_q2)

                # 计算目标值（包含熵正则化）
                target_value = reward_component + self.gamma * (1 - done) * (target_q - self.alpha_value * next_log_prob)

            # 计算当前Q值（两个critic）
            current_q1 = self.critics.get_critic(name, 1)(obs, action)
            current_q2 = self.critics.get_critic(name, 2)(obs, action)

            # 计算TD误差损失
            critic1_loss = F.mse_loss(current_q1, target_value)
            critic2_loss = F.mse_loss(current_q2, target_value)

            # 分别更新两个critic
            # 更新critic 1
            self.critic_optims[name].zero_grad()
            critic1_loss.backward(retain_graph=True)

            # 更新critic 2
            critic2_loss.backward()
            self.critic_optims[name].step()

            # 记录平均损失
            critic_losses[name] = (critic1_loss.item() + critic2_loss.item()) / 2.0

        return critic_losses

    def _update_actor(self, obs: torch.Tensor) -> float:
        """
        更新Actor网络

        Args:
            obs: 观测

        Returns:
            Actor损失
        """
        # 生成动作和对数概率
        action, log_prob = self.actor(obs, deterministic=False, with_logprob=True)

        # 计算所有评价器的Q值
        q_values = self.critics(obs, action)

        # 融合Q值
        fused_q = self.fusion_strategies.fuse_q_values(
            q_values, self.critic_weights, self.fusion_strategy
        )

        # 计算Actor损失（最大化Q值，最大化熵）
        actor_loss = (self.alpha_value * log_prob - fused_q).mean()

        # 反向传播
        self.actor_optim.zero_grad()
        actor_loss.backward()
        self.actor_optim.step()

        return actor_loss.item()

    def _update_alpha(self, obs: torch.Tensor) -> float:
        """
        更新熵正则化系数alpha

        Args:
            obs: 观测

        Returns:
            Alpha损失
        """
        if not self.auto_alpha:
            return 0.0

        # 生成动作和对数概率
        with torch.no_grad():
            _, log_prob = self.actor(obs, deterministic=False, with_logprob=True)

        # 计算alpha损失
        alpha_loss = -(self.log_alpha * (log_prob + self.target_entropy)).mean()

        # 反向传播
        self.alpha_optim.zero_grad()
        alpha_loss.backward()
        self.alpha_optim.step()

        return alpha_loss.item()

    def _soft_update_target(self) -> None:
        """软更新目标网络（双critic架构）"""
        for name in self.critics.critic_names:
            # 更新critic 1
            target_critic1 = self.critics_target.get_critic(name, 1)
            current_critic1 = self.critics.get_critic(name, 1)

            for target_param, param in zip(target_critic1.parameters(), current_critic1.parameters()):
                target_param.data.copy_(
                    target_param.data * (1.0 - self.tau) + param.data * self.tau
                )

            # 更新critic 2
            target_critic2 = self.critics_target.get_critic(name, 2)
            current_critic2 = self.critics.get_critic(name, 2)

            for target_param, param in zip(target_critic2.parameters(), current_critic2.parameters()):
                target_param.data.copy_(
                    target_param.data * (1.0 - self.tau) + param.data * self.tau
                )

    def set_critic_weights(self, weights: Dict[str, float]) -> None:
        """
        设置评价器权重

        Args:
            weights: 权重字典
        """
        # 确保权重归一化
        total_weight = sum(weights.values())
        self.critic_weights = {k: v / total_weight for k, v in weights.items()}

    def get_critic_weights(self) -> Dict[str, float]:
        """获取当前评价器权重"""
        return self.critic_weights.copy()
