# Multi-Critic SAC 技术文档

欢迎来到Multi-Critic SAC项目的技术文档中心。本文档体系提供了项目的完整技术说明和使用指南。

## 📚 文档目录

### 核心文档
- [**项目结构说明**](Project_Structure.md) - 完整的项目目录结构和模块功能说明
- [**系统架构概览**](Architecture_Overview.md) - 系统架构图和组件交互流程
- [**API参考手册**](API_Reference.md) - 详细的API文档和使用示例

### 技术深入
- [**数据流分析**](Data_Flow.md) - 数据在各组件间的传递流程
- [**训练流程详解**](Training_Pipeline.md) - 完整的训练流程文档
- [**配置指南**](Configuration_Guide.md) - 参数配置说明和调优建议

### 实用指南
- [**快速开始**](Quick_Start.md) - 快速上手指南
- [**故障排除**](Troubleshooting.md) - 常见问题和解决方案
- [**性能优化**](Performance_Optimization.md) - 性能调优建议

## 🎯 Multi-Critic SAC 核心特性

### 双Critic架构
- 每个目标有两个独立的critic网络（Q1, Q2）
- 使用min(Q1, Q2)策略减少过估计偏差
- 支持多目标优化的稳定训练

### 多目标评价器
- **避障评价器**：基于雷达数据评估碰撞风险
- **导航评价器**：评估智能体朝向目标的导航效果
- **环境适应评价器**：评估智能体对海洋环境的适应性

### 灵活的融合策略
- 加权平均融合
- 最小值/最大值融合
- 中位数融合
- 可扩展的自定义融合策略

## 🚀 快速导航

### 新用户
1. 阅读 [项目结构说明](Project_Structure.md) 了解项目组织
2. 查看 [快速开始](Quick_Start.md) 运行第一个示例
3. 参考 [配置指南](Configuration_Guide.md) 调整参数

### 开发者
1. 研读 [系统架构概览](Architecture_Overview.md) 理解设计思路
2. 查阅 [API参考手册](API_Reference.md) 了解接口详情
3. 分析 [数据流分析](Data_Flow.md) 掌握数据传递

### 研究者
1. 深入 [训练流程详解](Training_Pipeline.md) 理解算法实现
2. 参考 [性能优化](Performance_Optimization.md) 提升训练效果
3. 查看 [故障排除](Troubleshooting.md) 解决技术问题

## 📖 文档约定

### 代码示例
所有代码示例都经过测试验证，可以直接运行。

### 图表说明
- 使用Mermaid语法绘制流程图和架构图
- 提供中文标注和详细说明

### 版本信息
- 文档版本：v1.0.0
- 对应代码版本：修复后的双critic架构
- 最后更新：2024年

## 🤝 贡献指南

如果您发现文档中的错误或希望改进内容，请：
1. 提交Issue描述问题
2. 提供Pull Request修复
3. 参与文档讨论和改进

## 📞 技术支持

如有技术问题，请参考：
1. [故障排除](Troubleshooting.md) 查找常见问题解决方案
2. [API参考手册](API_Reference.md) 查找接口使用方法
3. 提交GitHub Issue获取帮助
