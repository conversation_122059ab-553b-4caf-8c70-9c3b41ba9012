"""
Actor网络架构实现

实现了SAC算法中的Actor网络，用于策略学习和动作生成。
支持连续动作空间和重参数化技巧。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, List, Union, Optional
from torch.distributions import Normal


class ActorNetwork(nn.Module):
    """
    Actor网络实现
    
    使用重参数化技巧生成连续动作，支持随机策略和确定性策略。
    
    Args:
        state_shape: 状态空间维度
        action_shape: 动作空间维度
        hidden_sizes: 隐藏层大小列表
        max_action: 最大动作值
        device: 计算设备
        activation: 激活函数
    """
    
    def __init__(
        self,
        state_shape: Union[int, Tuple[int, ...]],
        action_shape: Union[int, Tuple[int, ...]],
        hidden_sizes: List[int] = [256, 256],
        max_action: float = 1.0,
        device: Union[str, torch.device] = "cpu",
        activation: nn.Module = nn.ReLU,
        log_std_min: float = -20.0,
        log_std_max: float = 2.0
    ) -> None:
        super().__init__()
        
        self.device = device
        self.state_dim = int(np.prod(state_shape))
        self.action_dim = int(np.prod(action_shape))
        self.max_action = max_action
        self.log_std_min = log_std_min
        self.log_std_max = log_std_max
        
        # 构建共享特征提取层
        layers = []
        input_dim = self.state_dim
        
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(input_dim, hidden_size),
                activation(),
                nn.LayerNorm(hidden_size)  # 添加层归一化
            ])
            input_dim = hidden_size
            
        self.feature_extractor = nn.Sequential(*layers)
        
        # 均值和标准差输出层
        self.mean_layer = nn.Linear(input_dim, self.action_dim)
        self.log_std_layer = nn.Linear(input_dim, self.action_dim)
        
        self.to(device)
        
    def forward(
        self, 
        state: torch.Tensor,
        deterministic: bool = False,
        with_logprob: bool = True
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播生成动作
        
        Args:
            state: 状态张量 [batch_size, state_dim]
            deterministic: 是否使用确定性策略
            with_logprob: 是否计算对数概率
            
        Returns:
            action: 动作张量 [batch_size, action_dim]
            log_prob: 对数概率张量 [batch_size, 1] (如果with_logprob=True)
        """
        # 确保输入在正确的设备上
        state = state.to(self.device)
        
        # 展平输入
        if len(state.shape) > 2:
            state = state.view(state.shape[0], -1)
            
        # 特征提取
        features = self.feature_extractor(state)
        
        # 计算均值和标准差
        mean = self.mean_layer(features)
        log_std = self.log_std_layer(features)
        
        # 限制标准差范围
        log_std = torch.clamp(log_std, self.log_std_min, self.log_std_max)
        std = torch.exp(log_std)
        
        # 创建正态分布
        normal = Normal(mean, std)
        
        if deterministic:
            # 确定性策略：直接使用均值
            action_raw = mean
        else:
            # 随机策略：从分布中采样
            action_raw = normal.rsample()  # 重参数化采样
            
        # 使用tanh激活函数将动作限制在[-1, 1]范围内
        action = torch.tanh(action_raw)
        
        # 缩放到实际动作范围
        action = action * self.max_action
        
        if with_logprob and not deterministic:
            # 计算对数概率（考虑tanh变换的雅可比行列式）
            log_prob = normal.log_prob(action_raw).sum(dim=-1, keepdim=True)
            
            # 修正tanh变换的对数概率
            log_prob -= torch.log(self.max_action * (1 - action.pow(2)) + 1e-6).sum(dim=-1, keepdim=True)
            
            return action, log_prob
        else:
            return action, None
            
    def get_action(
        self, 
        state: torch.Tensor,
        deterministic: bool = False
    ) -> torch.Tensor:
        """
        获取动作（不计算对数概率）
        
        Args:
            state: 状态张量
            deterministic: 是否使用确定性策略
            
        Returns:
            动作张量
        """
        action, _ = self.forward(state, deterministic=deterministic, with_logprob=False)
        return action
        
    def evaluate_actions(
        self, 
        state: torch.Tensor, 
        action: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        评估给定状态-动作对的对数概率和熵
        
        Args:
            state: 状态张量
            action: 动作张量
            
        Returns:
            log_prob: 对数概率张量
            entropy: 熵张量
        """
        # 确保输入在正确的设备上
        state = state.to(self.device)
        action = action.to(self.device)
        
        # 展平输入
        if len(state.shape) > 2:
            state = state.view(state.shape[0], -1)
        if len(action.shape) > 2:
            action = action.view(action.shape[0], -1)
            
        # 特征提取
        features = self.feature_extractor(state)
        
        # 计算均值和标准差
        mean = self.mean_layer(features)
        log_std = self.log_std_layer(features)
        log_std = torch.clamp(log_std, self.log_std_min, self.log_std_max)
        std = torch.exp(log_std)
        
        # 创建正态分布
        normal = Normal(mean, std)
        
        # 反向tanh变换
        action_raw = torch.atanh(torch.clamp(action / self.max_action, -0.999, 0.999))
        
        # 计算对数概率
        log_prob = normal.log_prob(action_raw).sum(dim=-1, keepdim=True)
        
        # 修正tanh变换的对数概率
        log_prob -= torch.log(self.max_action * (1 - action.pow(2)) + 1e-6).sum(dim=-1, keepdim=True)
        
        # 计算熵
        entropy = normal.entropy().sum(dim=-1, keepdim=True)
        
        return log_prob, entropy
