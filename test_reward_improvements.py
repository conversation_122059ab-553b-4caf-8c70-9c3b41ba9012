"""
测试奖励函数改进效果
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_critic_sac.environments.marine_environment import MarineEnvironment


def test_reward_improvements():
    """测试奖励函数改进效果"""
    print("🔍 测试奖励函数改进效果...")
    
    # 创建环境
    env = MarineEnvironment(
        map_size=(50.0, 50.0),
        num_obstacles=5,
        max_episode_steps=100
    )
    
    # 运行几个回合来测试奖励
    total_rewards = []
    component_rewards = {
        'obstacle_avoidance': [],
        'navigation_guidance': [],
        'environment_adaptation': [],
        'survival': []
    }
    
    for episode in range(5):
        obs, info = env.reset()
        episode_reward = 0.0
        episode_components = {key: 0.0 for key in component_rewards.keys()}
        
        print(f"\n回合 {episode + 1}:")
        print(f"  初始位置: ({info['agent_position'][0]:.1f}, {info['agent_position'][1]:.1f})")
        print(f"  目标位置: ({info['target_position'][0]:.1f}, {info['target_position'][1]:.1f})")
        print(f"  初始距离: {info['distance_to_target']:.1f}")
        
        for step in range(50):  # 限制步数
            # 随机动作
            action = env.action_space.sample()
            
            # 执行步骤
            next_obs, reward, terminated, truncated, info = env.step(action)
            
            episode_reward += reward
            
            # 累积分量奖励
            if 'reward_components' in info:
                for key in component_rewards.keys():
                    if key in info['reward_components']:
                        episode_components[key] += info['reward_components'][key]
                    elif key in info:
                        episode_components[key] += info[key]
            
            if terminated or truncated:
                break
        
        total_rewards.append(episode_reward)
        for key in component_rewards.keys():
            component_rewards[key].append(episode_components[key])
        
        print(f"  最终位置: ({info['agent_position'][0]:.1f}, {info['agent_position'][1]:.1f})")
        print(f"  最终距离: {info['distance_to_target']:.1f}")
        print(f"  回合奖励: {episode_reward:.2f}")
        print(f"  步数: {step + 1}")
        print(f"  是否成功: {'是' if info['distance_to_target'] < 2.0 else '否'}")
        print(f"  是否碰撞: {'是' if info.get('collision', False) else '否'}")
        
        # 显示奖励分量
        print("  奖励分量:")
        for key, value in episode_components.items():
            print(f"    {key}: {value:.2f}")
    
    # 统计结果
    print(f"\n📊 统计结果 (5个回合):")
    print(f"  平均总奖励: {np.mean(total_rewards):.2f} ± {np.std(total_rewards):.2f}")
    print(f"  奖励范围: [{np.min(total_rewards):.2f}, {np.max(total_rewards):.2f}]")
    
    print(f"\n  平均分量奖励:")
    for key, values in component_rewards.items():
        if values:  # 如果有数据
            print(f"    {key}: {np.mean(values):.2f} ± {np.std(values):.2f}")
    
    # 判断改进效果
    avg_reward = np.mean(total_rewards)
    if avg_reward > -1000:
        print(f"\n✅ 奖励函数改进成功！平均奖励 {avg_reward:.2f} 在合理范围内。")
    elif avg_reward > -5000:
        print(f"\n⚠️ 奖励函数有所改进，但仍需优化。平均奖励 {avg_reward:.2f}")
    else:
        print(f"\n❌ 奖励函数仍需进一步改进。平均奖励 {avg_reward:.2f} 过低。")


def test_single_step_rewards():
    """测试单步奖励的合理性"""
    print("\n🔍 测试单步奖励合理性...")
    
    env = MarineEnvironment(
        map_size=(30.0, 30.0),
        num_obstacles=3,
        max_episode_steps=50
    )
    
    obs, info = env.reset()
    print(f"初始状态:")
    print(f"  智能体位置: ({info['agent_position'][0]:.1f}, {info['agent_position'][1]:.1f})")
    print(f"  目标位置: ({info['target_position'][0]:.1f}, {info['target_position'][1]:.1f})")
    print(f"  距离: {info['distance_to_target']:.1f}")
    
    # 测试不同类型的动作
    test_actions = [
        ([0.0, 0.0], "静止"),
        ([1.0, 0.0], "全速前进"),
        ([0.5, 0.5], "前进+右转"),
        ([-0.5, 0.0], "后退"),
        ([0.0, 1.0], "原地右转")
    ]
    
    for action_values, action_desc in test_actions:
        # 重置到相同状态
        obs, info = env.reset(seed=42)
        
        # 执行动作
        action = np.array(action_values)
        next_obs, reward, terminated, truncated, info = env.step(action)
        
        print(f"\n动作: {action_desc} {action_values}")
        print(f"  总奖励: {reward:.3f}")
        
        if 'reward_components' in info:
            print(f"  奖励分量:")
            for key, value in info['reward_components'].items():
                print(f"    {key}: {value:.3f}")


if __name__ == '__main__':
    test_reward_improvements()
    test_single_step_rewards()
