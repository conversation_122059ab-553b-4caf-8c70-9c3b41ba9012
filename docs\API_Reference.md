# API参考手册

本文档提供Multi-Critic SAC项目所有公共API的详细说明，包括类、方法、参数和使用示例。

## 📚 目录

- [网络模块 (networks)](#网络模块-networks)
- [策略模块 (policy)](#策略模块-policy)
- [环境模块 (environments)](#环境模块-environments)
- [工具模块 (utils)](#工具模块-utils)

## 🧠 网络模块 (networks)

### ActorNetwork

Actor网络实现，用于策略学习和动作生成。

#### 类定义

```python
class ActorNetwork(nn.Module):
    def __init__(
        self,
        state_shape: Union[int, Tuple[int, ...]],
        action_shape: Union[int, Tuple[int, ...]],
        hidden_sizes: List[int] = [256, 256],
        max_action: float = 1.0,
        device: Union[str, torch.device] = "cpu",
        activation: nn.Module = nn.ReLU,
        log_std_min: float = -20.0,
        log_std_max: float = 2.0
    )
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `state_shape` | Union[int, Tuple] | - | 状态空间维度 |
| `action_shape` | Union[int, Tuple] | - | 动作空间维度 |
| `hidden_sizes` | List[int] | [256, 256] | 隐藏层大小列表 |
| `max_action` | float | 1.0 | 最大动作值 |
| `device` | Union[str, torch.device] | "cpu" | 计算设备 |
| `activation` | nn.Module | nn.ReLU | 激活函数 |
| `log_std_min` | float | -20.0 | 对数标准差最小值 |
| `log_std_max` | float | 2.0 | 对数标准差最大值 |

#### 主要方法

##### forward()

```python
def forward(
    self, 
    state: torch.Tensor,
    deterministic: bool = False,
    with_logprob: bool = True
) -> Tuple[torch.Tensor, Optional[torch.Tensor]]
```

**功能**：前向传播生成动作

**参数**：
- `state`: 状态张量 [batch_size, state_dim]
- `deterministic`: 是否使用确定性策略
- `with_logprob`: 是否计算对数概率

**返回**：
- `action`: 动作张量 [batch_size, action_dim]
- `log_prob`: 对数概率张量 [batch_size, 1]（可选）

**使用示例**：

```python
import torch
from multi_critic_sac.networks.actor_network import ActorNetwork

# 创建Actor网络
actor = ActorNetwork(
    state_shape=(46,),
    action_shape=(2,),
    hidden_sizes=[256, 256],
    device='cuda'
)

# 生成动作
state = torch.randn(32, 46, device='cuda')
action, log_prob = actor(state, deterministic=False, with_logprob=True)

print(f"动作形状: {action.shape}")  # [32, 2]
print(f"对数概率形状: {log_prob.shape}")  # [32, 1]
```

##### get_action()

```python
def get_action(
    self, 
    state: torch.Tensor,
    deterministic: bool = False
) -> torch.Tensor
```

**功能**：获取动作（不计算对数概率）

**使用示例**：

```python
# 获取确定性动作
action = actor.get_action(state, deterministic=True)
```

### MultiCriticNetworks

多评价器网络集合，实现双critic架构。

#### 类定义

```python
class MultiCriticNetworks(nn.Module):
    def __init__(
        self,
        state_shape: Union[int, Tuple[int, ...]],
        action_shape: Union[int, Tuple[int, ...]],
        num_critics: int = 3,
        hidden_sizes: List[int] = [256, 256],
        device: Union[str, torch.device] = "cpu",
        critic_names: Optional[List[str]] = None
    )
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `state_shape` | Union[int, Tuple] | - | 状态空间维度 |
| `action_shape` | Union[int, Tuple] | - | 动作空间维度 |
| `num_critics` | int | 3 | 评价器数量 |
| `hidden_sizes` | List[int] | [256, 256] | 隐藏层大小列表 |
| `device` | Union[str, torch.device] | "cpu" | 计算设备 |
| `critic_names` | Optional[List[str]] | None | 评价器名称列表 |

#### 主要方法

##### forward()

```python
def forward(
    self, 
    state: torch.Tensor, 
    action: torch.Tensor,
    critic_name: Optional[str] = None,
    return_both: bool = False
) -> Union[torch.Tensor, Dict[str, torch.Tensor], Tuple[Dict, Dict]]
```

**功能**：前向传播计算Q值（双critic架构）

**参数**：
- `state`: 状态张量
- `action`: 动作张量
- `critic_name`: 指定评价器名称
- `return_both`: 是否返回两个critic的Q值

**使用示例**：

```python
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks

# 创建多评价器网络
critics = MultiCriticNetworks(
    state_shape=(46,),
    action_shape=(2,),
    num_critics=3,
    hidden_sizes=[256, 256],
    device='cuda'
)

state = torch.randn(32, 46, device='cuda')
action = torch.randn(32, 2, device='cuda')

# 获取最小Q值（默认）
min_q_values = critics(state, action)
print(f"最小Q值: {list(min_q_values.keys())}")

# 获取两个critic的Q值
q1_values, q2_values = critics(state, action, return_both=True)
print(f"Q1值: {list(q1_values.keys())}")
print(f"Q2值: {list(q2_values.keys())}")

# 获取特定评价器的Q值
obstacle_q = critics(state, action, critic_name='obstacle_avoidance')
print(f"避障Q值形状: {obstacle_q.shape}")
```

##### get_critic()

```python
def get_critic(self, critic_name: str, critic_id: int = 1) -> CriticNetwork
```

**功能**：获取指定的评价器网络

**参数**：
- `critic_name`: 评价器名称
- `critic_id`: 评价器ID (1 或 2)

## 🎯 策略模块 (policy)

### MultiCriticSACPolicy

Multi-Critic SAC策略的核心实现。

#### 类定义

```python
class MultiCriticSACPolicy(BasePolicy):
    def __init__(
        self,
        actor: ActorNetwork,
        critics: MultiCriticNetworks,
        actor_optim: torch.optim.Optimizer,
        critic_optims: Dict[str, torch.optim.Optimizer],
        action_space,
        tau: float = 0.005,
        gamma: float = 0.99,
        alpha: float = 0.2,
        auto_alpha: bool = True,
        target_entropy: Optional[float] = None,
        fusion_strategy: str = "weighted_average",
        critic_weights: Optional[Dict[str, float]] = None,
        device: Union[str, torch.device] = "cpu"
    )
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `actor` | ActorNetwork | - | Actor网络 |
| `critics` | MultiCriticNetworks | - | 多评价器网络 |
| `actor_optim` | torch.optim.Optimizer | - | Actor优化器 |
| `critic_optims` | Dict[str, torch.optim.Optimizer] | - | 评价器优化器字典 |
| `action_space` | - | - | 动作空间 |
| `tau` | float | 0.005 | 软更新系数 |
| `gamma` | float | 0.99 | 折扣因子 |
| `alpha` | float | 0.2 | 熵正则化系数 |
| `auto_alpha` | bool | True | 是否自动调整alpha |
| `target_entropy` | Optional[float] | None | 目标熵值 |
| `fusion_strategy` | str | "weighted_average" | Q值融合策略 |
| `critic_weights` | Optional[Dict[str, float]] | None | 评价器权重 |
| `device` | Union[str, torch.device] | "cpu" | 计算设备 |

#### 主要方法

##### learn()

```python
def learn(
    self,
    batch: RolloutBatchProtocol,
    batch_size: Optional[int] = None,
    repeat: int = 1,
    **kwargs: Any
) -> MultiCriticSACTrainingStats
```

**功能**：策略学习更新

**使用示例**：

```python
import torch
import gymnasium as gym
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks

# 创建网络
actor = ActorNetwork(state_shape=(46,), action_shape=(2,))
critics = MultiCriticNetworks(state_shape=(46,), action_shape=(2,))

# 创建优化器
actor_optim = torch.optim.Adam(actor.parameters(), lr=3e-4)
critic_optims = {
    name: torch.optim.Adam(
        list(critics.get_critic(name, 1).parameters()) + 
        list(critics.get_critic(name, 2).parameters()), 
        lr=3e-4
    ) for name in critics.critic_names
}

# 创建策略
action_space = gym.spaces.Box(low=-1.0, high=1.0, shape=(2,))
policy = MultiCriticSACPolicy(
    actor=actor,
    critics=critics,
    actor_optim=actor_optim,
    critic_optims=critic_optims,
    action_space=action_space,
    fusion_strategy='weighted_average',
    critic_weights={
        'obstacle_avoidance': 0.4,
        'navigation_guidance': 0.4,
        'environment_adaptation': 0.2
    }
)

# 训练（需要准备batch数据）
# stats = policy.learn(batch)
```

### MultiCriticSACTrainingStats

训练统计信息类。

#### 类定义

```python
@dataclass
class MultiCriticSACTrainingStats:
    actor_loss: float
    critic_losses: Dict[str, float]
    alpha_loss: float
    alpha: float
    q_values: Dict[str, float]
    fused_q_value: float
```

#### 方法

##### get_loss_stats_dict()

```python
def get_loss_stats_dict(self) -> Dict[str, float]
```

**功能**：获取损失统计字典，用于与Tianshou训练器兼容

## 🌊 环境模块 (environments)

### MarineEnvironment

海上智能体仿真环境。

#### 类定义

```python
class MarineEnvironment(gym.Env):
    def __init__(
        self,
        map_size: Tuple[float, float] = (100.0, 100.0),
        max_episode_steps: int = 1000,
        num_obstacles: int = 10,
        radar_range: float = 20.0,
        radar_beams: int = 36,
        max_thrust: float = 5.0,
        max_steering: float = 2.0,
        dt: float = 0.1,
        seed: Optional[int] = None
    )
```

#### 使用示例

```python
from multi_critic_sac.environments.marine_environment import MarineEnvironment

# 创建环境
env = MarineEnvironment(
    map_size=(100.0, 100.0),
    num_obstacles=10,
    max_episode_steps=1000
)

# 环境交互
obs, info = env.reset()
print(f"观测空间: {env.observation_space}")
print(f"动作空间: {env.action_space}")

action = env.action_space.sample()
next_obs, reward, terminated, truncated, info = env.step(action)

print(f"奖励: {reward}")
print(f"奖励分量: {info['reward_components']}")
```

## 🔧 工具模块 (utils)

### FusionStrategies

Q值融合策略实现。

#### 主要方法

##### fuse_q_values()

```python
def fuse_q_values(
    self,
    q_values: Dict[str, torch.Tensor],
    weights: Dict[str, float],
    strategy: str = "weighted_average",
    fusion_network: Optional[torch.nn.Module] = None
) -> torch.Tensor
```

**功能**：融合多个Q值

**支持的策略**：
- `weighted_average`: 加权平均
- `minimum`: 最小值
- `maximum`: 最大值
- `median`: 中位数

**使用示例**：

```python
from multi_critic_sac.utils.fusion_strategies import FusionStrategies

fusion = FusionStrategies()

# 模拟Q值
q_values = {
    'obstacle_avoidance': torch.tensor([[10.0], [5.0]]),
    'navigation_guidance': torch.tensor([[8.0], [12.0]]),
    'environment_adaptation': torch.tensor([[6.0], [8.0]])
}

weights = {
    'obstacle_avoidance': 0.4,
    'navigation_guidance': 0.4,
    'environment_adaptation': 0.2
}

# 加权平均融合
fused_q = fusion.fuse_q_values(q_values, weights, 'weighted_average')
print(f"融合Q值: {fused_q}")

# 最小值融合
min_q = fusion.fuse_q_values(q_values, weights, 'minimum')
print(f"最小Q值: {min_q}")
```

## 📝 使用注意事项

### 1. 设备一致性
确保所有网络和数据在同一设备上：

```python
device = 'cuda' if torch.cuda.is_available() else 'cpu'
actor = ActorNetwork(..., device=device)
critics = MultiCriticNetworks(..., device=device)
```

### 2. 优化器配置
为双critic架构正确配置优化器：

```python
critic_optims = {}
for name in critics.critic_names:
    params_1 = list(critics.get_critic(name, 1).parameters())
    params_2 = list(critics.get_critic(name, 2).parameters())
    all_params = params_1 + params_2
    critic_optims[name] = torch.optim.Adam(all_params, lr=3e-4)
```

### 3. 奖励分解
确保环境返回正确的奖励分解信息：

```python
# 环境step方法应返回
info['reward_components'] = {
    'obstacle_avoidance': obstacle_reward,
    'navigation_guidance': navigation_reward,
    'environment_adaptation': adaptation_reward
}
```
