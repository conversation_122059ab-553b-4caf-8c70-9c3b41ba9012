# 项目结构说明

本文档详细说明Multi-Critic SAC项目的目录结构、模块功能和组件依赖关系。

## 📁 项目目录结构

```
Multi-Critic_SAC_tianshou_V1/
├── multi_critic_sac/                 # 核心算法包
│   ├── __init__.py                   # 包初始化文件
│   ├── networks/                     # 神经网络模块
│   │   ├── __init__.py
│   │   ├── actor_network.py          # Actor网络实现
│   │   └── multi_critic_networks.py  # 多评价器网络实现
│   ├── policy/                       # 策略模块
│   │   ├── __init__.py
│   │   └── multi_critic_sac_policy.py # Multi-Critic SAC策略
│   ├── environments/                 # 环境模块
│   │   ├── __init__.py
│   │   ├── marine_environment.py     # 海上智能体环境
│   │   └── reward_functions.py       # 奖励函数实现
│   ├── utils/                        # 工具模块
│   │   ├── __init__.py
│   │   └── fusion_strategies.py      # Q值融合策略
│   └── examples/                     # 示例和脚本
│       ├── __init__.py
│       ├── simple_example.py         # 简单使用示例
│       ├── training_script.py        # 训练脚本
│       └── evaluation_script.py      # 评估脚本
├── docs/                             # 技术文档
│   ├── README.md                     # 文档索引
│   ├── Project_Structure.md          # 项目结构说明（本文档）
│   ├── Architecture_Overview.md      # 系统架构概览
│   ├── API_Reference.md              # API参考手册
│   ├── Data_Flow.md                  # 数据流分析
│   ├── Training_Pipeline.md          # 训练流程详解
│   ├── Configuration_Guide.md        # 配置指南
│   └── Troubleshooting.md            # 故障排除
├── log/                              # 训练日志和模型保存
│   └── multi_critic_sac/
├── tests/                            # 测试文件
│   ├── test_implementation.py
│   ├── test_multi_critic_sac_fixed.py
│   └── test_reward_improvements.py
├── README.md                         # 项目主文档
├── requirements.txt                  # 依赖包列表
├── setup.py                          # 安装配置
└── MULTI_CRITIC_SAC_FIXES.md        # 修复报告
```

## 🧩 核心模块功能

### 1. networks/ - 神经网络模块

#### `actor_network.py`
**功能**：实现SAC算法的Actor网络
- **类**：`ActorNetwork`
- **职责**：
  - 策略网络实现
  - 重参数化技巧
  - 连续动作生成
  - 对数概率计算

**关键特性**：
- 支持确定性和随机策略
- 使用tanh激活函数限制动作范围
- 实现熵正则化

#### `multi_critic_networks.py`
**功能**：实现多评价器双critic架构
- **类**：`CriticNetwork`, `MultiCriticNetworks`
- **职责**：
  - 管理多个独立的评价器
  - 实现双critic架构（每个目标有Q1和Q2）
  - Q值计算和融合
  - 参数管理

**关键特性**：
- 三个评价器：避障、导航、环境适应
- 每个评价器有两个critic网络
- 支持min(Q1, Q2)策略

### 2. policy/ - 策略模块

#### `multi_critic_sac_policy.py`
**功能**：Multi-Critic SAC算法的核心实现
- **类**：`MultiCriticSACPolicy`, `MultiCriticSACTrainingStats`
- **职责**：
  - 策略学习和更新
  - 多评价器训练协调
  - 损失计算和优化
  - 目标网络软更新

**关键特性**：
- 继承Tianshou的BasePolicy
- 支持分解奖励训练
- 实现双critic更新机制
- 提供详细的训练统计

### 3. environments/ - 环境模块

#### `marine_environment.py`
**功能**：海上智能体仿真环境
- **类**：`MarineEnvironment`
- **职责**：
  - 环境状态管理
  - 物理仿真（动力学、碰撞检测）
  - 观测生成（雷达、状态、环境信息）
  - 奖励计算和分解

**关键特性**：
- 符合Gymnasium接口标准
- 46维观测空间
- 2维连续动作空间
- 支持向量化环境

#### `reward_functions.py`
**功能**：多目标奖励函数实现
- **类**：`RewardFunctions`
- **职责**：
  - 避障奖励计算
  - 导航奖励计算
  - 环境适应奖励计算
  - 奖励分解和权重管理

**关键特性**：
- 三个独立的奖励分量
- 可配置的权重系统
- 基础存活奖励

### 4. utils/ - 工具模块

#### `fusion_strategies.py`
**功能**：Q值融合策略实现
- **类**：`FusionStrategies`, `LearnedFusionNetwork`, `AdaptiveFusionWeights`
- **职责**：
  - 多种融合策略实现
  - 学习的融合网络
  - 自适应权重调整

**关键特性**：
- 加权平均、最小值、最大值、中位数融合
- 支持自定义融合策略
- 可扩展的架构设计

### 5. examples/ - 示例模块

#### `training_script.py`
**功能**：完整的训练脚本
- **职责**：
  - 环境和网络初始化
  - 训练流程管理
  - 参数配置处理
  - 日志记录和模型保存

#### `evaluation_script.py`
**功能**：模型评估脚本
- **职责**：
  - 训练模型加载
  - 性能评估
  - 结果可视化
  - 统计分析

#### `simple_example.py`
**功能**：简单使用示例
- **职责**：
  - 快速演示
  - 功能验证
  - 学习参考

## 🔗 组件依赖关系

```mermaid
graph TD
    A[training_script.py] --> B[MultiCriticSACPolicy]
    A --> C[MarineEnvironment]
    A --> D[ActorNetwork]
    A --> E[MultiCriticNetworks]
    
    B --> D
    B --> E
    B --> F[FusionStrategies]
    
    C --> G[RewardFunctions]
    
    E --> H[CriticNetwork]
    
    I[Tianshou Framework] --> B
    I --> J[Collector]
    I --> K[Trainer]
    
    L[PyTorch] --> D
    L --> E
    L --> H
    
    M[Gymnasium] --> C
```

## 📊 模块间接口

### 主要数据流
1. **环境 → 策略**：观测数据、奖励分解
2. **策略 → 网络**：状态、动作、目标值
3. **网络 → 策略**：Q值、策略输出
4. **融合器 → 策略**：融合后的Q值

### 关键接口
- `MultiCriticSACPolicy.learn()` - 策略学习入口
- `MultiCriticNetworks.forward()` - Q值计算
- `MarineEnvironment.step()` - 环境交互
- `FusionStrategies.fuse_q_values()` - Q值融合

## 🎯 设计原则

### 模块化设计
- 每个模块职责单一明确
- 接口清晰，耦合度低
- 易于测试和维护

### 可扩展性
- 支持新的评价器类型
- 支持新的融合策略
- 支持新的环境类型

### 兼容性
- 与Tianshou框架完全兼容
- 遵循Gymnasium环境标准
- 支持PyTorch生态系统

## 📝 开发指南

### 添加新评价器
1. 在`MultiCriticNetworks`中添加新的critic
2. 在`RewardFunctions`中实现对应的奖励函数
3. 更新`MarineEnvironment`的奖励分解逻辑

### 添加新融合策略
1. 在`FusionStrategies`中实现新方法
2. 更新`MultiCriticSACPolicy`的融合调用
3. 添加相应的测试用例

### 自定义环境
1. 继承`MarineEnvironment`或实现Gymnasium接口
2. 确保返回正确的奖励分解信息
3. 适配观测空间和动作空间
