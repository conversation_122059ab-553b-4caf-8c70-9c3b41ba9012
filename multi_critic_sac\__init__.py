"""
Multi-Critic SAC (Soft Actor-Critic) 算法实现

基于Tianshou框架实现的多评价器SAC算法，专门用于解决海上智能体的多目标优化问题。

主要特性：
- 多个独立的评价器网络，分别针对不同的优化目标
- 可配置的Q值融合机制
- 支持向量化环境训练
- 完整的海上智能体环境模拟

作者：AI Assistant
版本：1.0.0
"""

from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.environments.reward_functions import RewardFunctions
from multi_critic_sac.utils.fusion_strategies import FusionStrategies

__version__ = "1.0.0"
__author__ = "AI Assistant"

__all__ = [
    "MultiCriticSACPolicy",
    "MultiCriticNetworks", 
    "ActorNetwork",
    "MarineEnvironment",
    "RewardFunctions",
    "FusionStrategies"
]
