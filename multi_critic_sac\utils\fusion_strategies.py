"""
Q值融合策略实现

提供多种Q值融合策略，用于将多个评价器的Q值组合成最终的Q值。
"""

import torch
import torch.nn.functional as F
from typing import Dict, List, Optional, Union
import numpy as np


class FusionStrategies:
    """
    Q值融合策略类
    
    提供多种融合策略来组合多个评价器的Q值：
    - weighted_average: 加权平均
    - minimum: 最小值（保守策略）
    - maximum: 最大值（乐观策略）
    - median: 中位数
    - learned_fusion: 学习的融合（需要额外的融合网络）
    """
    
    def __init__(self):
        self.supported_strategies = [
            "weighted_average",
            "minimum", 
            "maximum",
            "median",
            "learned_fusion"
        ]
        
    def fuse_q_values(
        self,
        q_values: Dict[str, torch.Tensor],
        weights: Dict[str, float],
        strategy: str = "weighted_average",
        fusion_network: Optional[torch.nn.Module] = None
    ) -> torch.Tensor:
        """
        融合多个Q值
        
        Args:
            q_values: Q值字典，键为评价器名称，值为Q值张量
            weights: 权重字典
            strategy: 融合策略
            fusion_network: 学习的融合网络（仅在learned_fusion策略时使用）
            
        Returns:
            融合后的Q值张量
        """
        if strategy not in self.supported_strategies:
            raise ValueError(f"不支持的融合策略: {strategy}. 支持的策略: {self.supported_strategies}")
            
        if strategy == "weighted_average":
            return self._weighted_average_fusion(q_values, weights)
        elif strategy == "minimum":
            return self._minimum_fusion(q_values)
        elif strategy == "maximum":
            return self._maximum_fusion(q_values)
        elif strategy == "median":
            return self._median_fusion(q_values)
        elif strategy == "learned_fusion":
            return self._learned_fusion(q_values, fusion_network)
        else:
            raise NotImplementedError(f"融合策略 {strategy} 尚未实现")
            
    def _weighted_average_fusion(
        self,
        q_values: Dict[str, torch.Tensor],
        weights: Dict[str, float]
    ) -> torch.Tensor:
        """
        加权平均融合
        
        Args:
            q_values: Q值字典
            weights: 权重字典
            
        Returns:
            融合后的Q值
        """
        fused_q = None
        total_weight = 0.0
        
        for name, q_value in q_values.items():
            weight = weights.get(name, 0.0)
            if weight > 0:
                if fused_q is None:
                    fused_q = weight * q_value
                else:
                    fused_q += weight * q_value
                total_weight += weight
                
        if fused_q is None:
            raise ValueError("没有有效的Q值进行融合")
            
        # 归一化权重
        if total_weight > 0:
            fused_q = fused_q / total_weight
            
        return fused_q
        
    def _minimum_fusion(self, q_values: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        最小值融合（保守策略）
        
        Args:
            q_values: Q值字典
            
        Returns:
            融合后的Q值
        """
        q_list = list(q_values.values())
        if not q_list:
            raise ValueError("没有Q值进行融合")
            
        # 堆叠所有Q值并取最小值
        stacked_q = torch.stack(q_list, dim=-1)
        fused_q, _ = torch.min(stacked_q, dim=-1, keepdim=True)
        
        return fused_q
        
    def _maximum_fusion(self, q_values: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        最大值融合（乐观策略）
        
        Args:
            q_values: Q值字典
            
        Returns:
            融合后的Q值
        """
        q_list = list(q_values.values())
        if not q_list:
            raise ValueError("没有Q值进行融合")
            
        # 堆叠所有Q值并取最大值
        stacked_q = torch.stack(q_list, dim=-1)
        fused_q, _ = torch.max(stacked_q, dim=-1, keepdim=True)
        
        return fused_q
        
    def _median_fusion(self, q_values: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        中位数融合
        
        Args:
            q_values: Q值字典
            
        Returns:
            融合后的Q值
        """
        q_list = list(q_values.values())
        if not q_list:
            raise ValueError("没有Q值进行融合")
            
        # 堆叠所有Q值并取中位数
        stacked_q = torch.stack(q_list, dim=-1)
        fused_q = torch.median(stacked_q, dim=-1, keepdim=True)[0]
        
        return fused_q
        
    def _learned_fusion(
        self,
        q_values: Dict[str, torch.Tensor],
        fusion_network: Optional[torch.nn.Module]
    ) -> torch.Tensor:
        """
        学习的融合策略
        
        使用神经网络学习如何融合多个Q值
        
        Args:
            q_values: Q值字典
            fusion_network: 融合网络
            
        Returns:
            融合后的Q值
        """
        if fusion_network is None:
            raise ValueError("学习的融合策略需要提供融合网络")
            
        q_list = list(q_values.values())
        if not q_list:
            raise ValueError("没有Q值进行融合")
            
        # 将所有Q值拼接作为融合网络的输入
        concatenated_q = torch.cat(q_list, dim=-1)
        fused_q = fusion_network(concatenated_q)
        
        return fused_q


class LearnedFusionNetwork(torch.nn.Module):
    """
    学习的融合网络
    
    用于学习如何最优地融合多个Q值的神经网络。
    """
    
    def __init__(
        self,
        num_critics: int,
        hidden_sizes: List[int] = [64, 32],
        device: Union[str, torch.device] = "cpu"
    ):
        """
        初始化融合网络
        
        Args:
            num_critics: 评价器数量
            hidden_sizes: 隐藏层大小列表
            device: 计算设备
        """
        super().__init__()
        
        self.num_critics = num_critics
        self.device = device
        
        # 构建网络层
        layers = []
        input_dim = num_critics  # 输入为各个评价器的Q值
        
        for hidden_size in hidden_sizes:
            layers.extend([
                torch.nn.Linear(input_dim, hidden_size),
                torch.nn.ReLU(),
                torch.nn.LayerNorm(hidden_size)
            ])
            input_dim = hidden_size
            
        # 输出层：输出融合后的单个Q值
        layers.append(torch.nn.Linear(input_dim, 1))
        
        self.network = torch.nn.Sequential(*layers)
        self.to(device)
        
    def forward(self, q_values: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            q_values: 拼接的Q值张量 [batch_size, num_critics]
            
        Returns:
            融合后的Q值 [batch_size, 1]
        """
        return self.network(q_values)


class AdaptiveFusionWeights(torch.nn.Module):
    """
    自适应融合权重
    
    根据当前状态动态调整各评价器的权重。
    """
    
    def __init__(
        self,
        state_dim: int,
        num_critics: int,
        hidden_sizes: List[int] = [64, 32],
        device: Union[str, torch.device] = "cpu"
    ):
        """
        初始化自适应权重网络
        
        Args:
            state_dim: 状态维度
            num_critics: 评价器数量
            hidden_sizes: 隐藏层大小列表
            device: 计算设备
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.num_critics = num_critics
        self.device = device
        
        # 构建网络层
        layers = []
        input_dim = state_dim
        
        for hidden_size in hidden_sizes:
            layers.extend([
                torch.nn.Linear(input_dim, hidden_size),
                torch.nn.ReLU(),
                torch.nn.LayerNorm(hidden_size)
            ])
            input_dim = hidden_size
            
        # 输出层：输出各评价器的权重
        layers.append(torch.nn.Linear(input_dim, num_critics))
        layers.append(torch.nn.Softmax(dim=-1))  # 确保权重和为1
        
        self.network = torch.nn.Sequential(*layers)
        self.to(device)
        
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播计算权重
        
        Args:
            state: 状态张量 [batch_size, state_dim]
            
        Returns:
            权重张量 [batch_size, num_critics]
        """
        return self.network(state)
