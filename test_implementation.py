"""
Multi-Critic SAC实现测试脚本

验证各个组件是否正常工作。
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """测试环境"""
    print("测试海上智能体环境...")
    
    from multi_critic_sac.environments.marine_environment import MarineEnvironment
    
    env = MarineEnvironment(
        map_size=(50.0, 50.0),
        num_obstacles=5,
        max_episode_steps=100
    )
    
    # 测试重置
    obs, info = env.reset()
    print(f"  观测空间形状: {obs.shape}")
    print(f"  观测范围: [{obs.min():.2f}, {obs.max():.2f}]")
    print(f"  动作空间形状: {env.action_space.shape}")
    
    # 测试步进
    action = env.action_space.sample()
    next_obs, reward, terminated, truncated, info = env.step(action)
    
    print(f"  随机动作: {action}")
    print(f"  奖励: {reward:.2f}")
    print(f"  终止: {terminated}, 截断: {truncated}")
    print("  ✓ 环境测试通过")
    
    return env


def test_networks():
    """测试网络"""
    print("\n测试神经网络...")
    
    from multi_critic_sac.networks.actor_network import ActorNetwork
    from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    state_shape = (46,)
    action_shape = (2,)
    batch_size = 32
    
    # 测试Actor网络
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=[64, 64],
        device=device
    )
    
    # 测试前向传播
    state = torch.randn(batch_size, *state_shape, device=device)
    action, log_prob = actor(state, deterministic=False, with_logprob=True)
    
    print(f"  Actor输入形状: {state.shape}")
    print(f"  Actor输出形状: {action.shape}")
    print(f"  对数概率形状: {log_prob.shape}")
    print(f"  动作范围: [{action.min():.2f}, {action.max():.2f}]")
    
    # 测试多评价器网络
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=3,
        hidden_sizes=[64, 64],
        device=device
    )
    
    q_values = critics(state, action)
    print(f"  评价器数量: {len(q_values)}")
    print(f"  评价器名称: {list(q_values.keys())}")
    
    for name, q in q_values.items():
        print(f"  {name} Q值形状: {q.shape}, 范围: [{q.min():.2f}, {q.max():.2f}]")
    
    print("  ✓ 网络测试通过")
    
    return actor, critics


def test_fusion_strategies():
    """测试融合策略"""
    print("\n测试融合策略...")
    
    from multi_critic_sac.utils.fusion_strategies import FusionStrategies
    
    fusion = FusionStrategies()
    
    # 模拟Q值
    q_values = {
        'obstacle_avoidance': torch.tensor([[10.0], [5.0], [15.0]]),
        'navigation_guidance': torch.tensor([[8.0], [12.0], [6.0]]),
        'environment_adaptation': torch.tensor([[6.0], [8.0], [10.0]])
    }
    
    weights = {
        'obstacle_avoidance': 0.4,
        'navigation_guidance': 0.4,
        'environment_adaptation': 0.2
    }
    
    # 测试不同融合策略
    strategies = ['weighted_average', 'minimum', 'maximum', 'median']
    
    for strategy in strategies:
        try:
            fused_q = fusion.fuse_q_values(q_values, weights, strategy)
            print(f"  {strategy}: {fused_q.squeeze().tolist()}")
        except Exception as e:
            print(f"  {strategy}: 错误 - {e}")
    
    print("  ✓ 融合策略测试通过")


def test_reward_functions():
    """测试奖励函数"""
    print("\n测试奖励函数...")
    
    from multi_critic_sac.environments.reward_functions import RewardFunctions
    
    reward_func = RewardFunctions()
    
    # 模拟状态信息
    state_info = {
        'radar_data': np.array([10.0, 15.0, 8.0, 20.0] + [25.0] * 32),
        'position': np.array([10.0, 5.0]),
        'target_position': np.array([20.0, 15.0]),
        'previous_position': np.array([9.0, 4.0]),
        'heading': 0.5,
        'velocity': np.array([2.0, 1.0]),
        'ocean_current': np.array([1.0, 0.5]),
        'wind_force': np.array([0.5, -0.3]),
        'energy_consumption': 1.5
    }
    
    action = np.array([0.5, 0.2])
    
    # 计算奖励
    rewards = reward_func.compute_total_reward(state_info, action)
    
    print("  奖励分量:")
    for name, value in rewards.items():
        print(f"    {name}: {value:.2f}")
    
    print("  ✓ 奖励函数测试通过")


def test_policy():
    """测试策略"""
    print("\n测试Multi-Critic SAC策略...")
    
    from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy
    
    # 使用之前创建的网络
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    state_shape = (46,)
    action_shape = (2,)
    
    from multi_critic_sac.networks.actor_network import ActorNetwork
    from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
    
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=[64, 64],
        device=device
    )
    
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=3,
        hidden_sizes=[64, 64],
        device=device
    )
    
    # 创建优化器
    actor_optim = torch.optim.Adam(actor.parameters(), lr=3e-4)
    critic_optims = {
        name: torch.optim.Adam(critics.get_critic(name).parameters(), lr=3e-4)
        for name in critics.critic_names
    }
    
    # 创建虚拟动作空间
    import gymnasium as gym
    action_space = gym.spaces.Box(low=-1.0, high=1.0, shape=action_shape)
    
    # 创建策略
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=action_space,
        device=device
    )
    
    # 测试前向传播
    from tianshou.data import Batch
    
    obs = np.random.randn(5, *state_shape)
    batch = Batch(obs=obs)
    
    result = policy.forward(batch)
    print(f"  策略输出动作形状: {result.act.shape}")
    print(f"  动作范围: [{result.act.min():.2f}, {result.act.max():.2f}]")
    
    print("  ✓ 策略测试通过")


def test_integration():
    """集成测试"""
    print("\n进行集成测试...")
    
    # 创建环境
    env = test_environment()
    
    # 创建网络
    actor, critics = test_networks()
    
    # 测试一个完整的交互循环
    obs, info = env.reset()
    
    # 使用actor生成动作
    with torch.no_grad():
        obs_tensor = torch.tensor(obs, dtype=torch.float32, device=actor.device).unsqueeze(0)
        action = actor.get_action(obs_tensor, deterministic=True)
        action = action.cpu().numpy().squeeze()
    
    # 执行动作
    next_obs, reward, terminated, truncated, info = env.step(action)
    
    print(f"  集成测试 - 奖励: {reward:.2f}")
    print("  ✓ 集成测试通过")


def main():
    """主测试函数"""
    print("Multi-Critic SAC 实现测试")
    print("=" * 50)
    
    try:
        # 测试各个组件
        test_environment()
        test_networks()
        test_fusion_strategies()
        test_reward_functions()
        test_policy()
        test_integration()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！实现正常工作。")
        print("\n可以运行以下命令开始训练:")
        print("python multi_critic_sac/examples/simple_example.py")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
