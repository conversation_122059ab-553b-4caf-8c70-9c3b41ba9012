# Multi-Critic SAC (多评价器软演员-评论家算法)

基于Tianshou框架实现的多评价器SAC算法，专门用于解决海上智能体的多目标优化问题。

## 🌊 项目概述

Multi-Critic SAC是传统SAC算法的扩展版本，通过使用多个独立的评价器网络来处理多目标优化问题。每个评价器专门评估一个特定的目标，然后通过融合机制将多个Q值组合成最终的Q值用于训练actor网络。

### 主要特性

- **多评价器架构**: 支持多个独立的评价器网络，分别针对不同优化目标
- **灵活的融合策略**: 提供多种Q值融合方法（加权平均、最小值、最大值、中位数等）
- **海上智能体环境**: 完整的海上导航环境模拟，包含避障、目标导航和环境适应
- **向量化训练**: 支持向量化环境，加速训练过程
- **完整的中文文档**: 详细的中文注释和文档字符串

## 🎯 算法架构

### 三个评价器目标

1. **避障评价器** (`obstacle_avoidance`)
   - 基于雷达传感器数据评估碰撞风险
   - 距离越近惩罚越大，使用指数衰减函数

2. **引导评价器** (`navigation_guidance`)
   - 评估智能体朝向目标的导航效果
   - 考虑距离变化、航向偏差和速度对齐

3. **环境适应评价器** (`environment_adaptation`)
   - 评估智能体对洋流和风速的适应性
   - 鼓励利用环境力，节约能量消耗

### 观测空间设计

- **雷达数据**: 36个方向的雷达波束距离值 `[36]`
- **智能体状态**: 位置、速度、航向角、角速度 `[6]`
- **环境信息**: 洋流速度、风力 `[4]`
- **总观测维度**: 46维

### 动作空间设计

- **推进力**: `[-1, 1]` 控制前进/后退
- **转向力**: `[-1, 1]` 控制左转/右转

## 🚀 快速开始

### 安装依赖

```bash
pip install torch gymnasium tianshou numpy matplotlib
```

### 基本使用

```python
from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy

# 创建环境
env = MarineEnvironment(
    map_size=(100.0, 100.0),
    num_obstacles=10,
    max_episode_steps=1000
)

# 创建网络
actor = ActorNetwork(
    state_shape=env.observation_space.shape,
    action_shape=env.action_space.shape,
    hidden_sizes=[256, 256]
)

critics = MultiCriticNetworks(
    state_shape=env.observation_space.shape,
    action_shape=env.action_space.shape,
    num_critics=3,
    hidden_sizes=[256, 256]
)

# 创建策略
policy = MultiCriticSACPolicy(
    actor=actor,
    critics=critics,
    actor_optim=torch.optim.Adam(actor.parameters()),
    critic_optims={name: torch.optim.Adam(critics.get_critic(name).parameters()) 
                   for name in critics.critic_names},
    action_space=env.action_space,
    fusion_strategy='weighted_average',
    critic_weights={
        'obstacle_avoidance': 0.4,
        'navigation_guidance': 0.4,
        'environment_adaptation': 0.2
    }
)
```

### 运行示例

```bash
# 运行简单演示
python multi_critic_sac/examples/simple_example.py

# 完整训练
python multi_critic_sac/examples/training_script.py \
    --epoch 100 \
    --training-num 10 \
    --fusion-strategy weighted_average \
    --obstacle-weight 0.4 \
    --navigation-weight 0.4 \
    --adaptation-weight 0.2

# 评估训练好的模型
python multi_critic_sac/examples/evaluation_script.py \
    --model-path ./log/multi_critic_sac/policy.pth \
    --num-episodes 20 \
    --render
```

## 📊 融合策略

支持多种Q值融合策略：

### 1. 加权平均 (`weighted_average`)
```python
fused_q = Σ(weight_i * q_i) / Σ(weight_i)
```

### 2. 最小值 (`minimum`)
```python
fused_q = min(q_1, q_2, q_3)  # 保守策略
```

### 3. 最大值 (`maximum`)
```python
fused_q = max(q_1, q_2, q_3)  # 乐观策略
```

### 4. 中位数 (`median`)
```python
fused_q = median(q_1, q_2, q_3)  # 鲁棒策略
```

## 🏗️ 项目结构

```
multi_critic_sac/
├── __init__.py
├── networks/                    # 网络架构
│   ├── __init__.py
│   ├── multi_critic_networks.py    # 多评价器网络
│   └── actor_network.py            # Actor网络
├── policy/                      # 策略实现
│   ├── __init__.py
│   └── multi_critic_sac_policy.py  # Multi-Critic SAC策略
├── environments/                # 环境模块
│   ├── __init__.py
│   ├── marine_environment.py       # 海上智能体环境
│   └── reward_functions.py         # 奖励函数
├── utils/                       # 工具模块
│   ├── __init__.py
│   └── fusion_strategies.py        # 融合策略
└── examples/                    # 使用示例
    ├── __init__.py
    ├── simple_example.py           # 简单示例
    ├── training_script.py          # 训练脚本
    └── evaluation_script.py        # 评估脚本
```

## ⚙️ 配置参数

### 环境参数
- `map_size`: 地图大小，默认 `(100.0, 100.0)`
- `num_obstacles`: 障碍物数量，默认 `10`
- `max_episode_steps`: 最大步数，默认 `1000`
- `radar_range`: 雷达探测范围，默认 `20.0`

### 网络参数
- `hidden_sizes`: 隐藏层大小，默认 `[256, 256]`
- `num_critics`: 评价器数量，默认 `3`

### 训练参数
- `lr_actor`: Actor学习率，默认 `3e-4`
- `lr_critic`: Critic学习率，默认 `3e-4`
- `gamma`: 折扣因子，默认 `0.99`
- `tau`: 软更新系数，默认 `0.005`
- `alpha`: 熵正则化系数，默认 `0.2`

### 融合参数
- `fusion_strategy`: 融合策略，默认 `'weighted_average'`
- `obstacle_weight`: 避障权重，默认 `0.4`
- `navigation_weight`: 导航权重，默认 `0.4`
- `adaptation_weight`: 环境适应权重，默认 `0.2`

## 📈 性能监控

训练过程中会记录以下指标：

- **总奖励**: 所有评价器奖励的总和
- **分量奖励**: 各个评价器的独立奖励
- **成功率**: 到达目标的回合比例
- **碰撞率**: 发生碰撞的回合比例
- **平均步数**: 每回合的平均步数
- **Q值**: 各评价器的Q值和融合后的Q值

## 🔧 自定义扩展

### 添加新的评价器

```python
# 1. 修改评价器名称
critic_names = [
    "obstacle_avoidance",
    "navigation_guidance", 
    "environment_adaptation",
    "your_new_critic"  # 新增评价器
]

# 2. 实现对应的奖励函数
def compute_your_new_reward(self, state_info, action):
    # 实现你的奖励逻辑
    return reward_value

# 3. 更新权重配置
critic_weights = {
    'obstacle_avoidance': 0.3,
    'navigation_guidance': 0.3,
    'environment_adaptation': 0.2,
    'your_new_critic': 0.2
}
```

### 自定义融合策略

```python
def custom_fusion_strategy(q_values, weights):
    # 实现你的融合逻辑
    # q_values: Dict[str, torch.Tensor]
    # weights: Dict[str, float]
    return fused_q_value
```

## 📝 引用

如果您在研究中使用了这个实现，请引用：

```bibtex
@software{multi_critic_sac,
  title={Multi-Critic SAC: A Multi-Objective Reinforcement Learning Algorithm},
  author={jiexu},
  year={2025},
  url={https://github.com/jieXu24/Muti-critic_SAC_tianshou_V1.git}
}
```

## 📄 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：<EMAIL>
