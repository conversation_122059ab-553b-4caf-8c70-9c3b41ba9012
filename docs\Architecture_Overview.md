# 系统架构概览

本文档详细介绍Multi-Critic SAC算法的系统架构设计、组件交互流程和核心创新点。

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "训练环境层"
        ENV[MarineEnvironment<br/>海上智能体环境]
        RF[RewardFunctions<br/>奖励函数]
        ENV --> RF
    end
    
    subgraph "策略层"
        POLICY[MultiCriticSACPolicy<br/>多评价器SAC策略]
        FS[FusionStrategies<br/>融合策略]
        POLICY --> FS
    end
    
    subgraph "网络层"
        ACTOR[ActorNetwork<br/>策略网络]
        MCN[MultiCriticNetworks<br/>多评价器网络]
        
        subgraph "评价器组"
            OA[避障评价器<br/>Q1_oa, Q2_oa]
            NG[导航评价器<br/>Q1_ng, Q2_ng]
            EA[环境适应评价器<br/>Q1_ea, Q2_ea]
        end
        
        MCN --> OA
        MCN --> NG
        MCN --> EA
    end
    
    subgraph "框架层"
        TS[Tianshou Framework]
        COL[Collector<br/>数据收集器]
        TRAINER[Trainer<br/>训练器]
        
        TS --> COL
        TS --> TRAINER
    end
    
    ENV --> POLICY
    POLICY --> ACTOR
    POLICY --> MCN
    TRAINER --> POLICY
    COL --> ENV
    
    style ENV fill:#e1f5fe
    style POLICY fill:#f3e5f5
    style ACTOR fill:#e8f5e8
    style MCN fill:#fff3e0
    style TS fill:#fce4ec
```

## 🎯 核心创新点

### 1. 双Critic多目标架构

```mermaid
graph LR
    subgraph "传统SAC"
        S1[State] --> Q1[Q1]
        S1 --> Q2[Q2]
        A1[Action] --> Q1
        A1 --> Q2
        Q1 --> MIN1[min(Q1,Q2)]
        Q2 --> MIN1
    end
    
    subgraph "Multi-Critic SAC"
        S2[State] --> OA1[Q1_避障]
        S2 --> OA2[Q2_避障]
        S2 --> NG1[Q1_导航]
        S2 --> NG2[Q2_导航]
        S2 --> EA1[Q1_环境]
        S2 --> EA2[Q2_环境]
        
        A2[Action] --> OA1
        A2 --> OA2
        A2 --> NG1
        A2 --> NG2
        A2 --> EA1
        A2 --> EA2
        
        OA1 --> MINOA[min(Q1,Q2)_避障]
        OA2 --> MINOA
        NG1 --> MINNG[min(Q1,Q2)_导航]
        NG2 --> MINNG
        EA1 --> MINEA[min(Q1,Q2)_环境]
        EA2 --> MINEA
        
        MINOA --> FUSION[融合策略]
        MINNG --> FUSION
        MINEA --> FUSION
        FUSION --> FINAL[最终Q值]
    end
```

### 2. 奖励分解机制

```mermaid
graph TD
    ENV_STATE[环境状态] --> RADAR[雷达数据]
    ENV_STATE --> POSITION[位置信息]
    ENV_STATE --> OCEAN[海洋环境]
    
    RADAR --> OA_REWARD[避障奖励]
    POSITION --> NG_REWARD[导航奖励]
    OCEAN --> EA_REWARD[环境适应奖励]
    
    OA_REWARD --> OA_CRITIC[避障评价器]
    NG_REWARD --> NG_CRITIC[导航评价器]
    EA_REWARD --> EA_CRITIC[环境适应评价器]
    
    SURVIVAL[存活奖励] --> TOTAL[总奖励]
    OA_REWARD --> TOTAL
    NG_REWARD --> TOTAL
    EA_REWARD --> TOTAL
```

## 🔄 组件交互流程

### 训练时序图

```mermaid
sequenceDiagram
    participant T as Trainer
    participant C as Collector
    participant E as Environment
    participant P as Policy
    participant A as Actor
    participant MC as MultiCritic
    participant F as FusionStrategy
    
    T->>C: collect(n_step)
    loop 收集经验
        C->>E: reset() / step(action)
        E->>E: 计算奖励分解
        E-->>C: obs, reward_components, done
        C->>P: forward(obs)
        P->>A: generate_action(obs)
        A-->>P: action, log_prob
        P-->>C: action
    end
    
    C-->>T: batch_data
    T->>P: learn(batch)
    
    loop 训练更新
        P->>MC: 计算当前Q值
        MC-->>P: q1_values, q2_values
        P->>F: fuse_q_values()
        F-->>P: fused_q_values
        P->>P: 计算损失和梯度
        P->>MC: 更新critic参数
        P->>A: 更新actor参数
    end
    
    P-->>T: training_stats
```

### 数据流向图

```mermaid
graph LR
    subgraph "输入层"
        OBS[观测<br/>46维]
        ACT[动作<br/>2维]
    end
    
    subgraph "处理层"
        RADAR[雷达数据<br/>36维]
        STATE[智能体状态<br/>6维]
        ENV_INFO[环境信息<br/>4维]
        
        OBS --> RADAR
        OBS --> STATE
        OBS --> ENV_INFO
    end
    
    subgraph "评价层"
        OA_Q[避障Q值]
        NG_Q[导航Q值]
        EA_Q[环境Q值]
        
        RADAR --> OA_Q
        STATE --> NG_Q
        ENV_INFO --> EA_Q
        ACT --> OA_Q
        ACT --> NG_Q
        ACT --> EA_Q
    end
    
    subgraph "融合层"
        WEIGHTS[权重配置]
        FUSION_FUNC[融合函数]
        FINAL_Q[最终Q值]
        
        OA_Q --> FUSION_FUNC
        NG_Q --> FUSION_FUNC
        EA_Q --> FUSION_FUNC
        WEIGHTS --> FUSION_FUNC
        FUSION_FUNC --> FINAL_Q
    end
```

## 🧠 算法核心逻辑

### 1. 多评价器更新算法

```python
def update_critics(self, obs, action, reward_components, next_obs, done):
    """多评价器更新伪代码"""
    for critic_name in ['obstacle_avoidance', 'navigation_guidance', 'environment_adaptation']:
        # 获取对应的奖励分量
        reward = reward_components[critic_name]
        
        # 计算目标Q值（使用目标网络的最小值）
        with torch.no_grad():
            next_action, next_log_prob = actor(next_obs)
            target_q1 = target_critics[critic_name + '_1'](next_obs, next_action)
            target_q2 = target_critics[critic_name + '_2'](next_obs, next_action)
            target_q = torch.min(target_q1, target_q2)
            target_value = reward + gamma * (1 - done) * (target_q - alpha * next_log_prob)
        
        # 更新两个critic网络
        current_q1 = critics[critic_name + '_1'](obs, action)
        current_q2 = critics[critic_name + '_2'](obs, action)
        
        loss1 = F.mse_loss(current_q1, target_value)
        loss2 = F.mse_loss(current_q2, target_value)
        
        # 反向传播
        optimizer.zero_grad()
        loss1.backward(retain_graph=True)
        loss2.backward()
        optimizer.step()
```

### 2. Q值融合策略

```python
def fuse_q_values(self, q_values, weights, strategy='weighted_average'):
    """Q值融合伪代码"""
    if strategy == 'weighted_average':
        fused_q = sum(weights[name] * q_values[name] for name in q_values.keys())
    elif strategy == 'minimum':
        fused_q = torch.min(torch.stack(list(q_values.values())), dim=0)[0]
    elif strategy == 'maximum':
        fused_q = torch.max(torch.stack(list(q_values.values())), dim=0)[0]
    elif strategy == 'median':
        fused_q = torch.median(torch.stack(list(q_values.values())), dim=0)[0]
    
    return fused_q
```

## 🎛️ 关键设计决策

### 1. 为什么选择双Critic架构？
- **减少过估计偏差**：SAC算法的核心特性
- **提高训练稳定性**：每个目标都有独立的双critic
- **保持算法一致性**：与标准SAC保持兼容

### 2. 为什么使用奖励分解？
- **目标独立性**：每个评价器专注于特定目标
- **训练效率**：避免目标间的相互干扰
- **可解释性**：清晰的目标-评价器对应关系

### 3. 为什么支持多种融合策略？
- **灵活性**：适应不同的应用场景
- **可扩展性**：支持自定义融合方法
- **实验性**：便于研究不同融合效果

## 📊 性能特征

### 计算复杂度
- **网络参数**：约3倍于标准SAC（3个目标 × 2个critic）
- **计算开销**：训练时间增加约2-3倍
- **内存占用**：约3倍于标准SAC

### 收敛特性
- **稳定性**：双critic架构提供更稳定的训练
- **收敛速度**：多目标分解可能略慢于单目标
- **最终性能**：在多目标任务上表现更优

### 扩展性
- **评价器数量**：理论上可支持任意数量
- **融合策略**：支持自定义实现
- **环境适配**：易于适配新的环境类型

## 🔧 架构优势

### 1. 模块化设计
- 每个组件职责明确
- 易于测试和调试
- 支持独立优化

### 2. 可扩展性
- 新增评价器简单
- 支持自定义融合策略
- 易于适配新环境

### 3. 兼容性
- 完全兼容Tianshou框架
- 遵循标准SAC算法
- 支持现有工具链

### 4. 可解释性
- 清晰的目标分解
- 独立的评价器输出
- 详细的训练统计
