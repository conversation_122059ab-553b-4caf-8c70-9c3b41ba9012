"""
Multi-Critic SAC修复验证脚本

验证修复后的Multi-Critic SAC算法的关键功能。
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy


def test_dual_critic_architecture():
    """测试双critic架构"""
    print("🔍 测试双critic架构...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    state_shape = (46,)
    action_shape = (2,)
    
    # 创建多评价器网络
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=3,
        hidden_sizes=[64, 64],
        device=device
    )
    
    # 验证双critic架构
    print(f"  ✅ 评价器名称: {critics.critic_names}")
    
    # 测试每个评价器是否有两个critic
    for name in critics.critic_names:
        critic1 = critics.get_critic(name, 1)
        critic2 = critics.get_critic(name, 2)
        print(f"  ✅ {name}: Critic1参数数量={sum(p.numel() for p in critic1.parameters())}, "
              f"Critic2参数数量={sum(p.numel() for p in critic2.parameters())}")
    
    # 测试前向传播
    batch_size = 32
    state = torch.randn(batch_size, *state_shape, device=device)
    action = torch.randn(batch_size, *action_shape, device=device)
    
    # 测试返回最小值
    min_q_values = critics(state, action)
    print(f"  ✅ 最小Q值形状: {[(name, q.shape) for name, q in min_q_values.items()]}")
    
    # 测试返回两个Q值
    q1_values, q2_values = critics(state, action, return_both=True)
    print(f"  ✅ Q1值形状: {[(name, q.shape) for name, q in q1_values.items()]}")
    print(f"  ✅ Q2值形状: {[(name, q.shape) for name, q in q2_values.items()]}")
    
    # 验证最小值计算正确
    for name in critics.critic_names:
        computed_min = torch.min(q1_values[name], q2_values[name])
        assert torch.allclose(min_q_values[name], computed_min, atol=1e-6)
    print("  ✅ 最小值计算正确")


def test_reward_decomposition():
    """测试奖励分解"""
    print("\n🔍 测试奖励分解...")
    
    # 创建环境
    env = MarineEnvironment(
        map_size=(50.0, 50.0),
        num_obstacles=3,
        max_episode_steps=100
    )
    
    # 重置环境
    obs, info = env.reset()
    print(f"  ✅ 初始观测形状: {obs.shape}")
    
    # 执行一步
    action = env.action_space.sample()
    next_obs, reward, terminated, truncated, info = env.step(action)
    
    # 检查奖励分解
    if 'reward_components' in info:
        print("  ✅ 奖励分解成功:")
        for name, value in info['reward_components'].items():
            print(f"    {name}: {value:.3f}")
    else:
        print("  ❌ 奖励分解失败")
    
    # 检查总奖励
    total_from_components = sum(info['reward_components'].values())
    print(f"  ✅ 总奖励: {reward:.3f}, 分量和: {total_from_components:.3f}")


def test_policy_integration():
    """测试策略集成"""
    print("\n🔍 测试策略集成...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建环境
    env = MarineEnvironment(
        map_size=(30.0, 30.0),
        num_obstacles=2,
        max_episode_steps=50
    )
    
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape
    
    # 创建网络
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=[64, 64],
        device=device
    )
    
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=3,
        hidden_sizes=[64, 64],
        device=device
    )
    
    # 创建优化器
    actor_optim = torch.optim.Adam(actor.parameters(), lr=3e-4)
    critic_optims = {}
    for name in critics.critic_names:
        params_1 = list(critics.get_critic(name, 1).parameters())
        params_2 = list(critics.get_critic(name, 2).parameters())
        all_params = params_1 + params_2
        critic_optims[name] = torch.optim.Adam(all_params, lr=3e-4)
    
    # 创建策略
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=env.action_space,
        device=device
    )
    
    print(f"  ✅ 策略创建成功")
    print(f"  ✅ 融合策略: {policy.fusion_strategy}")
    print(f"  ✅ 评价器权重: {policy.critic_weights}")
    
    # 测试前向传播
    obs, info = env.reset()
    from tianshou.data import Batch
    
    batch = Batch(obs=np.array([obs]))
    result = policy.forward(batch)
    
    print(f"  ✅ 前向传播成功，动作形状: {result.act.shape}")
    print(f"  ✅ 动作范围: [{result.act.min():.3f}, {result.act.max():.3f}]")


def test_training_step():
    """测试训练步骤"""
    print("\n🔍 测试训练步骤...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建简单的测试数据
    batch_size = 16
    state_dim = 46
    action_dim = 2
    
    # 模拟批次数据
    from tianshou.data import Batch
    
    batch = Batch(
        obs=np.random.randn(batch_size, state_dim),
        act=np.random.randn(batch_size, action_dim),
        rew=np.random.randn(batch_size),
        obs_next=np.random.randn(batch_size, state_dim),
        done=np.random.randint(0, 2, batch_size).astype(bool),
        info=[{
            'reward_components': {
                'obstacle_avoidance': np.random.randn(),
                'navigation_guidance': np.random.randn(),
                'environment_adaptation': np.random.randn()
            }
        } for _ in range(batch_size)]
    )
    
    # 创建网络和策略
    actor = ActorNetwork(
        state_shape=(state_dim,),
        action_shape=(action_dim,),
        hidden_sizes=[32, 32],
        device=device
    )
    
    critics = MultiCriticNetworks(
        state_shape=(state_dim,),
        action_shape=(action_dim,),
        num_critics=3,
        hidden_sizes=[32, 32],
        device=device
    )
    
    # 创建优化器
    actor_optim = torch.optim.Adam(actor.parameters(), lr=3e-4)
    critic_optims = {}
    for name in critics.critic_names:
        params_1 = list(critics.get_critic(name, 1).parameters())
        params_2 = list(critics.get_critic(name, 2).parameters())
        all_params = params_1 + params_2
        critic_optims[name] = torch.optim.Adam(all_params, lr=3e-4)
    
    # 创建策略
    import gymnasium as gym
    action_space = gym.spaces.Box(low=-1.0, high=1.0, shape=(action_dim,))
    
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=action_space,
        device=device
    )
    
    try:
        # 执行学习步骤
        stats = policy.learn(batch, repeat=1)
        
        print(f"  ✅ 训练步骤成功")
        print(f"  ✅ Actor损失: {stats.actor_loss:.4f}")
        print(f"  ✅ Alpha: {stats.alpha:.4f}")
        print(f"  ✅ Critic损失:")
        for name, loss in stats.critic_losses.items():
            print(f"    {name}: {loss:.4f}")
        
        # 测试统计信息兼容性
        loss_dict = stats.get_loss_stats_dict()
        print(f"  ✅ 损失统计字典包含 {len(loss_dict)} 个指标")
        
    except Exception as e:
        print(f"  ❌ 训练步骤失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("Multi-Critic SAC 修复验证")
    print("=" * 60)
    
    try:
        test_dual_critic_architecture()
        test_reward_decomposition()
        test_policy_integration()
        test_training_step()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！修复成功。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
