"""
多评价器网络架构实现

实现了Multi-Critic SAC算法中的多个独立评价器网络，
分别针对不同的优化目标进行Q值估计。
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from tianshou.utils.net.common import Net


class CriticNetwork(nn.Module):
    """
    单个评价器网络实现
    
    用于估计特定目标的Q值函数，支持连续动作空间。
    
    Args:
        state_shape: 状态空间维度
        action_shape: 动作空间维度  
        hidden_sizes: 隐藏层大小列表
        device: 计算设备
        activation: 激活函数
    """
    
    def __init__(
        self,
        state_shape: Union[int, Tuple[int, ...]],
        action_shape: Union[int, Tuple[int, ...]],
        hidden_sizes: List[int] = [256, 256],
        device: Union[str, torch.device] = "cpu",
        activation: nn.Module = nn.ReLU
    ) -> None:
        super().__init__()
        
        self.device = device
        self.state_dim = int(np.prod(state_shape))
        self.action_dim = int(np.prod(action_shape))
        
        # 构建网络层
        layers = []
        input_dim = self.state_dim + self.action_dim
        
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(input_dim, hidden_size),
                activation(),
                nn.LayerNorm(hidden_size)  # 添加层归一化提高训练稳定性
            ])
            input_dim = hidden_size
            
        # 输出层
        layers.append(nn.Linear(input_dim, 1))
        
        self.network = nn.Sequential(*layers)
        self.to(device)
        
    def forward(
        self, 
        state: torch.Tensor, 
        action: torch.Tensor
    ) -> torch.Tensor:
        """
        前向传播计算Q值
        
        Args:
            state: 状态张量 [batch_size, state_dim]
            action: 动作张量 [batch_size, action_dim]
            
        Returns:
            Q值张量 [batch_size, 1]
        """
        # 确保输入在正确的设备上
        state = state.to(self.device)
        action = action.to(self.device)
        
        # 展平输入
        if len(state.shape) > 2:
            state = state.view(state.shape[0], -1)
        if len(action.shape) > 2:
            action = action.view(action.shape[0], -1)
            
        # 拼接状态和动作
        x = torch.cat([state, action], dim=1)
        return self.network(x)


class MultiCriticNetworks(nn.Module):
    """
    多评价器网络集合（双critic架构）

    管理多个独立的评价器网络，每个目标有两个critic网络（Q1和Q2）：
    - 避障评价器：评估碰撞风险
    - 引导评价器：评估导航效果
    - 环境适应评价器：评估环境适应性
    """

    def __init__(
        self,
        state_shape: Union[int, Tuple[int, ...]],
        action_shape: Union[int, Tuple[int, ...]],
        num_critics: int = 3,
        hidden_sizes: List[int] = [256, 256],
        device: Union[str, torch.device] = "cpu",
        critic_names: Optional[List[str]] = None
    ) -> None:
        """
        初始化多评价器网络（双critic架构）

        Args:
            state_shape: 状态空间维度
            action_shape: 动作空间维度
            num_critics: 评价器数量
            hidden_sizes: 隐藏层大小列表
            device: 计算设备
            critic_names: 评价器名称列表
        """
        super().__init__()

        self.num_critics = num_critics
        self.device = device

        # 默认评价器名称
        if critic_names is None:
            self.critic_names = [
                "obstacle_avoidance",  # 避障评价器
                "navigation_guidance", # 引导评价器
                "environment_adaptation" # 环境适应评价器
            ][:num_critics]
        else:
            assert len(critic_names) == num_critics, "评价器名称数量必须与评价器数量一致"
            self.critic_names = critic_names

        # 创建双critic网络：每个目标有两个critic
        self.critics_1 = nn.ModuleDict()
        self.critics_2 = nn.ModuleDict()

        for name in self.critic_names:
            self.critics_1[name] = CriticNetwork(
                state_shape=state_shape,
                action_shape=action_shape,
                hidden_sizes=hidden_sizes,
                device=device
            )
            self.critics_2[name] = CriticNetwork(
                state_shape=state_shape,
                action_shape=action_shape,
                hidden_sizes=hidden_sizes,
                device=device
            )

        self.to(device)
        
    def forward(
        self,
        state: torch.Tensor,
        action: torch.Tensor,
        critic_name: Optional[str] = None,
        return_both: bool = False
    ) -> Union[torch.Tensor, Dict[str, torch.Tensor], Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]]:
        """
        前向传播计算Q值（双critic架构）

        Args:
            state: 状态张量
            action: 动作张量
            critic_name: 指定评价器名称，如果为None则返回所有评价器的Q值
            return_both: 是否返回两个critic的Q值

        Returns:
            根据参数返回不同格式的Q值
        """
        if critic_name is not None:
            assert critic_name in self.critics_1, f"未知的评价器名称: {critic_name}"
            q1 = self.critics_1[critic_name](state, action)
            q2 = self.critics_2[critic_name](state, action)

            if return_both:
                return q1, q2
            else:
                # 返回最小值（SAC双critic策略）
                return torch.min(q1, q2)
        else:
            # 返回所有评价器的Q值
            q1_values = {}
            q2_values = {}

            for name in self.critic_names:
                q1_values[name] = self.critics_1[name](state, action)
                q2_values[name] = self.critics_2[name](state, action)

            if return_both:
                return q1_values, q2_values
            else:
                # 返回最小值
                min_q_values = {}
                for name in self.critic_names:
                    min_q_values[name] = torch.min(q1_values[name], q2_values[name])
                return min_q_values
            
    def get_critic(self, critic_name: str, critic_id: int = 1) -> CriticNetwork:
        """
        获取指定的评价器网络

        Args:
            critic_name: 评价器名称
            critic_id: 评价器ID (1 或 2)

        Returns:
            评价器网络实例
        """
        if critic_id == 1:
            assert critic_name in self.critics_1, f"未知的评价器名称: {critic_name}"
            return self.critics_1[critic_name]
        else:
            assert critic_name in self.critics_2, f"未知的评价器名称: {critic_name}"
            return self.critics_2[critic_name]

    def get_all_critics(self) -> Tuple[Dict[str, CriticNetwork], Dict[str, CriticNetwork]]:
        """
        获取所有评价器网络

        Returns:
            两个评价器网络字典的元组
        """
        return dict(self.critics_1), dict(self.critics_2)

    def parameters_dict(self) -> Dict[str, List[torch.nn.Parameter]]:
        """
        获取各评价器的参数字典，用于分别设置优化器

        Returns:
            参数字典，键为评价器名称，值为参数列表（包含两个critic的参数）
        """
        params_dict = {}
        for name in self.critic_names:
            # 合并两个critic的参数
            params_1 = list(self.critics_1[name].parameters())
            params_2 = list(self.critics_2[name].parameters())
            params_dict[name] = params_1 + params_2
        return params_dict
