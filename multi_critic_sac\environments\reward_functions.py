"""
奖励函数实现

实现了Multi-Critic SAC算法中各个评价器对应的奖励函数：
- 避障奖励：基于雷达数据的碰撞风险评估
- 引导奖励：基于目标导航的效果评估
- 环境适应奖励：基于海洋环境适应性的评估
"""

import numpy as np
from typing import Dict, Tuple, Optional, Any
import math


class RewardFunctions:
    """
    奖励函数集合
    
    提供多种奖励函数用于评估智能体在不同目标上的表现。
    """
    
    def __init__(
        self,
        obstacle_weight: float = 1.0,
        navigation_weight: float = 1.0,
        adaptation_weight: float = 1.0,
        safety_distance: float = 5.0,
        target_tolerance: float = 2.0
    ):
        """
        初始化奖励函数
        
        Args:
            obstacle_weight: 避障奖励权重
            navigation_weight: 导航奖励权重
            adaptation_weight: 环境适应奖励权重
            safety_distance: 安全距离阈值
            target_tolerance: 目标容忍距离
        """
        self.obstacle_weight = obstacle_weight
        self.navigation_weight = navigation_weight
        self.adaptation_weight = adaptation_weight
        self.safety_distance = safety_distance
        self.target_tolerance = target_tolerance
        
    def compute_obstacle_avoidance_reward(
        self,
        radar_data: np.ndarray,
        collision_occurred: bool = False
    ) -> float:
        """
        计算避障奖励

        基于雷达传感器数据评估碰撞风险，距离越近惩罚越大。

        Args:
            radar_data: 雷达数据 [36,] 表示36个方向的距离
            collision_occurred: 是否发生碰撞

        Returns:
            避障奖励值
        """
        if collision_occurred:
            return -50.0  # 碰撞的严重惩罚（减少惩罚强度）

        # 找到最小距离
        min_distance = np.min(radar_data)

        if min_distance < self.safety_distance:
            # 距离越近，惩罚越大（线性惩罚，避免指数爆炸）
            danger_ratio = (self.safety_distance - min_distance) / self.safety_distance
            reward = -5.0 * danger_ratio  # 减少惩罚强度
        else:
            # 安全距离内给予小的正奖励
            reward = 0.5

        # 考虑多个方向的威胁（减少惩罚）
        dangerous_directions = np.sum(radar_data < self.safety_distance)
        if dangerous_directions > 0:
            reward -= dangerous_directions * 0.5  # 减少惩罚强度

        return reward * self.obstacle_weight
        
    def compute_navigation_guidance_reward(
        self,
        current_position: np.ndarray,
        target_position: np.ndarray,
        previous_position: np.ndarray,
        current_heading: float,
        velocity: np.ndarray
    ) -> float:
        """
        计算导航引导奖励
        
        基于智能体与目标点的距离和航向偏差设计奖励。
        
        Args:
            current_position: 当前位置 [x, y]
            target_position: 目标位置 [x, y]
            previous_position: 上一步位置 [x, y]
            current_heading: 当前航向角（弧度）
            velocity: 当前速度 [vx, vy]
            
        Returns:
            导航奖励值
        """
        # 计算到目标的距离
        current_distance = np.linalg.norm(current_position - target_position)
        previous_distance = np.linalg.norm(previous_position - target_position)
        
        # 距离变化奖励（减少奖励强度）
        distance_change = previous_distance - current_distance
        distance_reward = distance_change * 2.0  # 减少奖励强度

        # 到达目标奖励
        if current_distance < self.target_tolerance:
            distance_reward += 20.0  # 减少奖励强度

        # 基础距离惩罚（鼓励接近目标）
        distance_penalty = -current_distance * 0.01  # 轻微的距离惩罚

        # 航向奖励：计算当前航向与目标方向的偏差
        target_direction = target_position - current_position
        if np.linalg.norm(target_direction) > 0:
            target_direction = target_direction / np.linalg.norm(target_direction)
            current_direction = np.array([np.cos(current_heading), np.sin(current_heading)])

            # 计算角度偏差
            dot_product = np.clip(np.dot(current_direction, target_direction), -1.0, 1.0)
            angle_diff = np.arccos(dot_product)
            heading_reward = -angle_diff * 1.0  # 减少惩罚强度
        else:
            heading_reward = 0.0
            
        # 速度奖励：鼓励朝向目标的速度分量
        if np.linalg.norm(target_direction) > 0 and np.linalg.norm(velocity) > 0:
            velocity_direction = velocity / np.linalg.norm(velocity)
            velocity_alignment = np.dot(velocity_direction, target_direction)
            velocity_reward = velocity_alignment * 0.5  # 减少奖励强度
        else:
            velocity_reward = 0.0

        total_reward = distance_reward + distance_penalty + heading_reward + velocity_reward
        return total_reward * self.navigation_weight
        
    def compute_environment_adaptation_reward(
        self,
        velocity: np.ndarray,
        ocean_current: np.ndarray,
        wind_force: np.ndarray,
        action: np.ndarray,
        energy_consumption: float
    ) -> float:
        """
        计算环境适应奖励
        
        基于智能体利用或抵抗海洋环境力的效果设计奖励。
        
        Args:
            velocity: 智能体速度 [vx, vy]
            ocean_current: 洋流速度 [cx, cy]
            wind_force: 风力 [wx, wy]
            action: 智能体动作 [thrust, steering]
            energy_consumption: 能量消耗
            
        Returns:
            环境适应奖励值
        """
        # 洋流利用奖励：鼓励利用洋流（减少奖励强度）
        if np.linalg.norm(ocean_current) > 0 and np.linalg.norm(velocity) > 0:
            current_alignment = np.dot(velocity, ocean_current) / (
                np.linalg.norm(velocity) * np.linalg.norm(ocean_current)
            )
            current_reward = current_alignment * 0.5  # 减少奖励强度
        else:
            current_reward = 0.0

        # 风力适应奖励：根据风力调整策略（减少惩罚）
        wind_magnitude = np.linalg.norm(wind_force)
        if wind_magnitude > 2.0:  # 强风条件
            # 鼓励减少不必要的动作以节省能量
            action_magnitude = np.linalg.norm(action)
            wind_adaptation_reward = -action_magnitude * 0.1  # 减少惩罚强度
        else:
            wind_adaptation_reward = 0.0

        # 能量效率奖励：鼓励节能（减少惩罚）
        energy_reward = -energy_consumption * 0.01  # 减少惩罚强度

        # 稳定性奖励：鼓励平稳的运动（减少惩罚）
        velocity_magnitude = np.linalg.norm(velocity)
        if velocity_magnitude > 10.0:  # 速度过快
            stability_reward = -0.5  # 减少惩罚强度
        elif velocity_magnitude < 0.5:  # 速度过慢
            stability_reward = -0.2  # 减少惩罚强度
        else:
            stability_reward = 0.2  # 减少奖励强度

        total_reward = current_reward + wind_adaptation_reward + energy_reward + stability_reward
        return total_reward * self.adaptation_weight
        
    def compute_total_reward(
        self,
        state_info: Dict[str, Any],
        action: np.ndarray,
        collision_occurred: bool = False
    ) -> Dict[str, float]:
        """
        计算总奖励和各个分量奖励
        
        Args:
            state_info: 状态信息字典
            action: 动作
            collision_occurred: 是否发生碰撞
            
        Returns:
            包含各个奖励分量的字典
        """
        # 提取状态信息
        radar_data = state_info.get('radar_data', np.zeros(36))
        current_position = state_info.get('position', np.zeros(2))
        target_position = state_info.get('target_position', np.zeros(2))
        previous_position = state_info.get('previous_position', current_position)
        current_heading = state_info.get('heading', 0.0)
        velocity = state_info.get('velocity', np.zeros(2))
        ocean_current = state_info.get('ocean_current', np.zeros(2))
        wind_force = state_info.get('wind_force', np.zeros(2))
        energy_consumption = state_info.get('energy_consumption', 0.0)
        
        # 计算各个奖励分量
        obstacle_reward = self.compute_obstacle_avoidance_reward(
            radar_data, collision_occurred
        )
        
        navigation_reward = self.compute_navigation_guidance_reward(
            current_position, target_position, previous_position,
            current_heading, velocity
        )
        
        adaptation_reward = self.compute_environment_adaptation_reward(
            velocity, ocean_current, wind_force, action, energy_consumption
        )
        
        # 基础存活奖励（鼓励智能体保持运行）
        survival_reward = 1.0

        # 总奖励
        total_reward = obstacle_reward + navigation_reward + adaptation_reward + survival_reward

        return {
            'obstacle_avoidance': obstacle_reward,
            'navigation_guidance': navigation_reward,
            'environment_adaptation': adaptation_reward,
            'survival': survival_reward,
            'total': total_reward
        }
        
    def update_weights(
        self,
        obstacle_weight: Optional[float] = None,
        navigation_weight: Optional[float] = None,
        adaptation_weight: Optional[float] = None
    ) -> None:
        """
        更新奖励权重
        
        Args:
            obstacle_weight: 新的避障权重
            navigation_weight: 新的导航权重
            adaptation_weight: 新的适应权重
        """
        if obstacle_weight is not None:
            self.obstacle_weight = obstacle_weight
        if navigation_weight is not None:
            self.navigation_weight = navigation_weight
        if adaptation_weight is not None:
            self.adaptation_weight = adaptation_weight
