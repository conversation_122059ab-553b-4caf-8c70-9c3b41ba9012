"""
Multi-Critic SAC评估脚本

演示如何评估训练好的Multi-Critic SAC模型。
"""

import torch
import numpy as np
import argparse
import matplotlib.pyplot as plt
from typing import List, Dict, Any
import os
import sys

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from multi_critic_sac.environments.marine_environment import MarineEnvironment
from multi_critic_sac.networks.multi_critic_networks import MultiCriticNetworks
from multi_critic_sac.networks.actor_network import ActorNetwork
from multi_critic_sac.policy.multi_critic_sac_policy import MultiCriticSACPolicy


def get_args() -> argparse.Namespace:
    """获取命令行参数"""
    parser = argparse.ArgumentParser()
    
    # 模型参数
    parser.add_argument('--model-path', type=str, required=True,
                       help='训练好的模型路径')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备')
    
    # 评估参数
    parser.add_argument('--num-episodes', type=int, default=10,
                       help='评估回合数')
    parser.add_argument('--render', action='store_true',
                       help='是否渲染环境')
    parser.add_argument('--save-trajectory', action='store_true',
                       help='是否保存轨迹')
    parser.add_argument('--output-dir', type=str, default='./evaluation_results',
                       help='输出目录')
    
    # 环境参数（需要与训练时一致）
    parser.add_argument('--map-size', type=float, nargs=2, default=[100.0, 100.0],
                       help='地图大小')
    parser.add_argument('--num-obstacles', type=int, default=10,
                       help='障碍物数量')
    parser.add_argument('--max-episode-steps', type=int, default=1000,
                       help='最大步数')
    
    # 网络参数（需要与训练时一致）
    parser.add_argument('--hidden-sizes', type=int, nargs='+', default=[256, 256],
                       help='隐藏层大小')
    parser.add_argument('--num-critics', type=int, default=3,
                       help='评价器数量')
    
    return parser.parse_args()


def create_model(args: argparse.Namespace, env: MarineEnvironment) -> MultiCriticSACPolicy:
    """创建并加载模型"""
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape
    
    # 创建网络
    actor = ActorNetwork(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_sizes=args.hidden_sizes,
        device=args.device
    )
    
    critics = MultiCriticNetworks(
        state_shape=state_shape,
        action_shape=action_shape,
        num_critics=args.num_critics,
        hidden_sizes=args.hidden_sizes,
        device=args.device
    )
    
    # 创建虚拟优化器（评估时不需要）
    actor_optim = torch.optim.Adam(actor.parameters())
    critic_optims = {
        name: torch.optim.Adam(critics.get_critic(name).parameters())
        for name in critics.critic_names
    }
    
    # 创建策略
    policy = MultiCriticSACPolicy(
        actor=actor,
        critics=critics,
        actor_optim=actor_optim,
        critic_optims=critic_optims,
        action_space=env.action_space,
        device=args.device
    )
    
    # 加载模型权重
    if os.path.exists(args.model_path):
        policy.load_state_dict(torch.load(args.model_path, map_location=args.device))
        print(f"成功加载模型: {args.model_path}")
    else:
        raise FileNotFoundError(f"模型文件不存在: {args.model_path}")
    
    return policy


def evaluate_episode(
    env: MarineEnvironment, 
    policy: MultiCriticSACPolicy,
    render: bool = False,
    save_trajectory: bool = False
) -> Dict[str, Any]:
    """评估单个回合"""
    obs, info = env.reset()
    episode_reward = 0.0
    episode_length = 0
    trajectory = []
    reward_components = {
        'obstacle_avoidance': 0.0,
        'navigation_guidance': 0.0,
        'environment_adaptation': 0.0
    }
    
    terminated = False
    truncated = False
    
    while not (terminated or truncated):
        # 获取动作（确定性策略用于评估）
        with torch.no_grad():
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=policy.device).unsqueeze(0)
            action = policy.actor.get_action(obs_tensor, deterministic=True)
            action = action.cpu().numpy().squeeze()
        
        # 执行动作
        next_obs, reward, terminated, truncated, info = env.step(action)
        
        episode_reward += reward
        episode_length += 1
        
        # 累积奖励分量
        for key in reward_components:
            if key in info:
                reward_components[key] += info[key]
        
        # 保存轨迹
        if save_trajectory:
            trajectory.append({
                'obs': obs.copy(),
                'action': action.copy(),
                'reward': reward,
                'next_obs': next_obs.copy(),
                'info': info.copy()
            })
        
        # 渲染
        if render:
            env.render()
        
        obs = next_obs
    
    return {
        'episode_reward': episode_reward,
        'episode_length': episode_length,
        'reward_components': reward_components,
        'trajectory': trajectory if save_trajectory else None,
        'success': info.get('distance_to_target', float('inf')) < 2.0,
        'collision': info.get('collision', False)
    }


def plot_results(results: List[Dict[str, Any]], output_dir: str) -> None:
    """绘制评估结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取数据
    episode_rewards = [r['episode_reward'] for r in results]
    episode_lengths = [r['episode_length'] for r in results]
    success_rate = sum(r['success'] for r in results) / len(results)
    collision_rate = sum(r['collision'] for r in results) / len(results)
    
    # 奖励分量
    component_names = ['obstacle_avoidance', 'navigation_guidance', 'environment_adaptation']
    component_rewards = {name: [r['reward_components'][name] for r in results] 
                        for name in component_names}
    
    # 绘制回合奖励
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 3, 1)
    plt.plot(episode_rewards, 'b-', marker='o')
    plt.title('Episode Rewards')
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.grid(True)
    
    plt.subplot(2, 3, 2)
    plt.plot(episode_lengths, 'g-', marker='s')
    plt.title('Episode Lengths')
    plt.xlabel('Episode')
    plt.ylabel('Steps')
    plt.grid(True)
    
    # 奖励分量对比
    plt.subplot(2, 3, 3)
    x = np.arange(len(component_names))
    means = [np.mean(component_rewards[name]) for name in component_names]
    stds = [np.std(component_rewards[name]) for name in component_names]
    plt.bar(x, means, yerr=stds, capsize=5)
    plt.title('Reward Components')
    plt.xlabel('Component')
    plt.ylabel('Average Reward')
    plt.xticks(x, ['Obstacle', 'Navigation', 'Adaptation'], rotation=45)
    plt.grid(True)
    
    # 成功率和碰撞率
    plt.subplot(2, 3, 4)
    rates = [success_rate, collision_rate]
    labels = ['Success Rate', 'Collision Rate']
    colors = ['green', 'red']
    plt.bar(labels, rates, color=colors, alpha=0.7)
    plt.title('Success and Collision Rates')
    plt.ylabel('Rate')
    plt.ylim(0, 1)
    for i, rate in enumerate(rates):
        plt.text(i, rate + 0.02, f'{rate:.2f}', ha='center')
    plt.grid(True)
    
    # 奖励分布
    plt.subplot(2, 3, 5)
    plt.hist(episode_rewards, bins=10, alpha=0.7, edgecolor='black')
    plt.title('Reward Distribution')
    plt.xlabel('Reward')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    # 长度分布
    plt.subplot(2, 3, 6)
    plt.hist(episode_lengths, bins=10, alpha=0.7, edgecolor='black', color='orange')
    plt.title('Episode Length Distribution')
    plt.xlabel('Steps')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'evaluation_results.png'), dpi=300, bbox_inches='tight')
    plt.show()


def save_statistics(results: List[Dict[str, Any]], output_dir: str) -> None:
    """保存统计信息"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 计算统计信息
    episode_rewards = [r['episode_reward'] for r in results]
    episode_lengths = [r['episode_length'] for r in results]
    success_rate = sum(r['success'] for r in results) / len(results)
    collision_rate = sum(r['collision'] for r in results) / len(results)
    
    stats = {
        'num_episodes': len(results),
        'mean_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'min_reward': np.min(episode_rewards),
        'max_reward': np.max(episode_rewards),
        'mean_length': np.mean(episode_lengths),
        'std_length': np.std(episode_lengths),
        'success_rate': success_rate,
        'collision_rate': collision_rate
    }
    
    # 奖励分量统计
    component_names = ['obstacle_avoidance', 'navigation_guidance', 'environment_adaptation']
    for name in component_names:
        component_rewards = [r['reward_components'][name] for r in results]
        stats[f'{name}_mean'] = np.mean(component_rewards)
        stats[f'{name}_std'] = np.std(component_rewards)
    
    # 保存到文件
    stats_file = os.path.join(output_dir, 'statistics.txt')
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write("Multi-Critic SAC 评估统计\n")
        f.write("=" * 40 + "\n\n")
        
        f.write(f"评估回合数: {stats['num_episodes']}\n")
        f.write(f"平均奖励: {stats['mean_reward']:.2f} ± {stats['std_reward']:.2f}\n")
        f.write(f"奖励范围: [{stats['min_reward']:.2f}, {stats['max_reward']:.2f}]\n")
        f.write(f"平均步数: {stats['mean_length']:.1f} ± {stats['std_length']:.1f}\n")
        f.write(f"成功率: {stats['success_rate']:.2%}\n")
        f.write(f"碰撞率: {stats['collision_rate']:.2%}\n\n")
        
        f.write("奖励分量统计:\n")
        for name in component_names:
            mean_key = f'{name}_mean'
            std_key = f'{name}_std'
            f.write(f"  {name}: {stats[mean_key]:.2f} ± {stats[std_key]:.2f}\n")
    
    print(f"统计信息已保存到: {stats_file}")


def main():
    """主评估函数"""
    args = get_args()
    
    # 设置设备
    if args.device == 'auto':
        args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建环境
    env = MarineEnvironment(
        map_size=tuple(args.map_size),
        max_episode_steps=args.max_episode_steps,
        num_obstacles=args.num_obstacles
    )
    
    # 创建并加载模型
    policy = create_model(args, env)
    policy.eval()  # 设置为评估模式
    
    # 运行评估
    print(f"开始评估，共 {args.num_episodes} 个回合...")
    results = []
    
    for episode in range(args.num_episodes):
        result = evaluate_episode(
            env, policy, 
            render=args.render,
            save_trajectory=args.save_trajectory
        )
        results.append(result)
        
        print(f"回合 {episode + 1}: 奖励={result['episode_reward']:.2f}, "
              f"步数={result['episode_length']}, "
              f"成功={'是' if result['success'] else '否'}, "
              f"碰撞={'是' if result['collision'] else '否'}")
    
    # 分析和保存结果
    print("\n评估完成！正在生成报告...")
    save_statistics(results, args.output_dir)
    plot_results(results, args.output_dir)
    
    # 打印总结
    episode_rewards = [r['episode_reward'] for r in results]
    success_rate = sum(r['success'] for r in results) / len(results)
    collision_rate = sum(r['collision'] for r in results) / len(results)
    
    print(f"\n评估总结:")
    print(f"平均奖励: {np.mean(episode_rewards):.2f} ± {np.std(episode_rewards):.2f}")
    print(f"成功率: {success_rate:.2%}")
    print(f"碰撞率: {collision_rate:.2%}")


if __name__ == '__main__':
    main()
